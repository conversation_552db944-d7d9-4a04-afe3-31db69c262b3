import { Time } from "./Time";
import { Watcher } from "./Watcher";

/**
 * Interface for poolable objects
 */
export interface IPoolable {
    spawner?: PoolSpawner<any>;
    reuse(...args: any[]): void;
    unuse(): void;
    destroy(): void;
}

/**
 * Spawner callback function type
 */
export type SpawnerCallback<T> = (id: string, spawner: PoolSpawner<T>) => T;

/**
 * Despawner callback function type
 */
export type DespawnerCallback<T> = (item: T) => void;

/**
 * Pool spawner manages creation and lifecycle of pooled objects
 */
export class PoolSpawner<T extends IPoolable> {
    private _id: string;
    private _pool: Pool<T>;
    private _spawnedCall: SpawnerCallback<T>;
    private _despawnedCall: DespawnerCallback<T> | null;
    private _preloadCount: number = 0;
    private _spawneds: T[] = [];
    private _clearing: boolean = false;
    private _cullWatcher: Watcher | null = null;
    private _isPreload: boolean = false;
    
    public runNum: number = 0;
    public cullAbove: number = -1;

    constructor(
        id: string,
        pool: Pool<T>,
        spawnedCall: SpawnerCallback<T>,
        despawnedCall: DespawnerCallback<T> | null,
        preloadCount: number,
        cullAbove: number = -1
    ) {
        this._id = id;
        this._pool = pool;
        this._spawnedCall = spawnedCall;
        this._despawnedCall = despawnedCall;
        this._preloadCount = preloadCount;
        this.cullAbove = cullAbove;
    }

    get Id(): string {
        return this._id;
    }

    /**
     * Clear specific pool item
     */
    clearPoolItem(item: T): void {
        const index = this._spawneds.indexOf(item);
        if (index !== -1) {
            const removed = this._spawneds.splice(index, 1);
            this._clear(removed[0]);
        }
    }

    /**
     * Start smooth clearing process
     */
    smoothClear(): void {
        this._clearing = true;
        if (this._cullWatcher == null) {
            this._cullWatcher = Time.delay(60, this.cullDespawned, null, this, -1);
        } else {
            this._cullWatcher.times = 60;
        }
    }

    /**
     * Cull despawned objects
     */
    cullDespawned(): void {
        let targetCount = this.cullAbove;
        if (this._clearing || this.cullAbove <= 0) {
            targetCount = 0;
        }

        let removeCount = this._spawneds.length;
        if (removeCount > targetCount) {
            if (removeCount > 5) removeCount = 5;
            
            for (let i = removeCount - 1; i >= 0; i--) {
                const item = this._spawneds.pop();
                if (item) {
                    this._clear(item);
                }
            }
        }
    }

    /**
     * Preload instances
     */
    preloadInstances(): void {
        if (!this._isPreload) {
            this._isPreload = true;
            for (let i = 0; i < this._preloadCount; i++) {
                const item = this.spawnNew();
                if (item) {
                    item.unuse();
                    this._spawneds.push(item);
                }
            }
        }
    }

    /**
     * Spawn an instance
     */
    spawnInstance(...args: any[]): T | null {
        this._clearing = false;
        let item: T | null = null;
        
        if (this._spawneds.length > 0) {
            item = this._spawneds.shift() || null;
        } else {
            item = this.spawnNew();
        }

        if (item != null) {
            item.reuse(...args);
        }
        
        this.runNum++;
        return item;
    }

    /**
     * Despawn an instance
     */
    despawnInstance(item: T, destroy: boolean = false, triggerEvent: boolean = true): boolean {
        if (item == null) {
            cc.error("despawnInstance data is null, Pool-", this._pool.poolType);
            return false;
        }

        item.unuse();
        this.runNum--;

        if (destroy) {
            item.destroy();
        } else {
            this._spawneds.push(item);
        }

        if (triggerEvent && !this.despawnEvent(item)) {
            return false;
        }

        return true;
    }

    /**
     * Destroy the spawner
     */
    destroy(): void {
        if (this._pool.onDelSpawner != null) {
            this._pool.onDelSpawner.call(this._pool.target, this._id);
        }

        if (this._cullWatcher) {
            this._cullWatcher.cancel();
            this._cullWatcher = null;
        }

        this.clear();
    }

    /**
     * Clear all spawned objects
     */
    clear(): void {
        for (let i = 0; i < this._spawneds.length; i++) {
            this._clear(this._spawneds[i]);
        }
        this._spawneds.length = 0;
    }

    /**
     * Clear a single item
     */
    private _clear(item: T): void {
        item.destroy();
    }

    /**
     * Trigger despawn event
     */
    despawnEvent(item: T): boolean {
        try {
            if (this._despawnedCall != null) {
                this._despawnedCall.call(this._pool.target, item);
            }
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Spawn new instance
     */
    spawnNew(): T | null {
        if (this._pool == null) {
            cc.warn(this._pool, "pool has destroy!!!", this._id);
            return null;
        }

        if (this._spawnedCall == null) {
            cc.error("spawnedCall is null", this._id);
            return null;
        }

        return this._spawnedCall.call(this._pool.target, this._id, this);
    }
}

/**
 * Object pool for managing pooled objects
 */
export class Pool<T extends IPoolable> {
    private _spawners: { [key: string]: PoolSpawner<T> } = {};
    public poolType: string;
    public target: any;
    public onDelSpawner?: (id: string) => void;

    constructor(poolType: string, target: any) {
        this._spawners = cc.js.createMap();
        this.poolType = poolType;
        this.target = target;
    }

    /**
     * Get spawner by ID
     */
    getSpawner(id: string): PoolSpawner<T> | null {
        const spawner = this._spawners[id];
        if (spawner == null) {
            cc.warn("Pool.getSpawner null", this.poolType, id);
            return null;
        }
        return spawner;
    }

    /**
     * Clear all spawners
     */
    _clear(): void {
        const spawners = Object.values(this._spawners);
        for (let i = 0; i < spawners.length; i++) {
            const spawner = spawners[i];
            delete this._spawners[spawner.Id];
            spawner.destroy();
        }
    }

    /**
     * Destroy the pool
     */
    _destroy(): void {
        for (const id in this._spawners) {
            this._spawners[id].destroy();
        }
        this._spawners = null!;
    }

    /**
     * Create a new spawner
     */
    createSpawner(
        id: string,
        spawnedCall: SpawnerCallback<T>,
        despawnedCall: DespawnerCallback<T> | null = null,
        preloadCount: number = 0,
        cullAbove: number = -1
    ): PoolSpawner<T> {
        let spawner = this._spawners[id];
        if (spawner != null) {
            cc.error("createSpawner already create id:", id);
            return spawner;
        }

        spawner = new PoolSpawner(id, this, spawnedCall, despawnedCall, preloadCount, cullAbove);
        this._spawners[id] = spawner;
        return spawner;
    }

    /**
     * Delete a spawner
     */
    delSpawner(id: string): void {
        const spawner = this._spawners[id];
        if (spawner != null) {
            spawner.destroy();
            delete this._spawners[id];
        }
    }

    /**
     * Check if spawner exists
     */
    isExitsSpawner(id: string): boolean {
        return this._spawners[id] != null;
    }

    /**
     * Despawn an object
     */
    despawn(item: T, destroy: boolean = false): void {
        if (item != null) {
            if (item.spawner) {
                item.spawner.despawnInstance(item, destroy);
                if (!destroy) {
                    item.spawner.smoothClear();
                }
            }
        } else {
            cc.warn(this.poolType, "data is null");
        }
    }

    /**
     * Spawn an object
     */
    spawn(id: string, ...args: any[]): T | null {
        const spawner = this._spawners[id];
        if (spawner == null) {
            cc.error(this.poolType, "can't find spawner id:", id);
            return null;
        }
        return spawner.spawnInstance(...args);
    }
}

/**
 * Pool manager for managing multiple pools
 */
class PoolManagerClass {
    private _pools: Map<string, Pool<any>> = new Map();

    /**
     * Check if pool exists
     */
    isExist(poolType: string): boolean {
        return this._pools.get(poolType) != null;
    }

    /**
     * Create a new pool
     */
    create<T extends IPoolable>(poolType: string, target: any): Pool<T> {
        let pool = this._pools.get(poolType);
        if (pool != null) {
            cc.error("Pool has Created:", poolType);
            return pool;
        }

        if (target == null) {
            cc.error("PoolManager.create target null", poolType);
        }

        pool = new Pool<T>(poolType, target);
        this._pools.set(poolType, pool);
        return pool;
    }

    /**
     * Destroy a pool
     */
    destroy(poolType: string): void {
        const pool = this._pools.get(poolType);
        if (pool != null) {
            pool._destroy();
            this._pools.delete(poolType);
        }
    }

    /**
     * Clear a pool
     */
    clear(poolType: string): void {
        const pool = this._pools.get(poolType);
        if (pool != null) {
            pool._clear();
        }
    }

    /**
     * Get pool by type
     */
    getPool<T extends IPoolable>(poolType: string): Pool<T> | null {
        return this._pools.get(poolType) || null;
    }

    /**
     * Get all pool types
     */
    getAllPoolTypes(): string[] {
        return Array.from(this._pools.keys());
    }

    /**
     * Get pool statistics
     */
    getPoolStats(poolType: string): { spawnerCount: number; totalObjects: number } | null {
        const pool = this._pools.get(poolType);
        if (!pool) return null;

        let spawnerCount = 0;
        let totalObjects = 0;

        for (const id in (pool as any)._spawners) {
            spawnerCount++;
            const spawner = (pool as any)._spawners[id];
            totalObjects += spawner.runNum;
        }

        return { spawnerCount, totalObjects };
    }
}

// Export singleton instance
export const PoolManager = new PoolManagerClass();

/**
 * Base class for pool managers
 */
export class PoolMgrBase<T extends IPoolable> {
    protected _pool: Pool<T> | null = null;
    protected _poolType: string = "PoolMgrBase";
    public SpawnerName: string = "PoolItem";

    constructor(poolType: string) {
        this._poolType = poolType;
        this.initPool();
    }

    /**
     * Initialize the pool
     */
    protected initPool(): void {
        if (!PoolManager.isExist(this._poolType)) {
            this._pool = PoolManager.create<T>(this._poolType, this);
        }
    }

    /**
     * Component create delegate
     */
    compCreateDelegate(className: string, spawner: PoolSpawner<T>): T | null {
        const ClassConstructor = cc.js.getClassByName(className);
        if (ClassConstructor) {
            const instance = new ClassConstructor() as T;
            instance.spawner = spawner;
            return instance;
        }

        cc.error("compCreateDelegate can't find class by name", className);
        return null;
    }

    /**
     * Despawn an object
     */
    despawn(item: T): void {
        if (this._pool != null) {
            this._pool.despawn(item);
        } else if (item != null) {
            item.destroy();
        }
    }

    /**
     * Spawn an object
     */
    spawn(): T | null {
        if (this._pool == null) {
            return null;
        }

        if (!this._pool.isExitsSpawner(this.SpawnerName)) {
            this._pool.createSpawner(this.SpawnerName, this.compCreateDelegate.bind(this), null, 0);
        }

        return this._pool.spawn(this.SpawnerName);
    }

    /**
     * Clone an object with properties
     */
    clone(source: Partial<T>): T | null {
        const item = this.spawn();
        if (item) {
            for (const key in source) {
                (item as any)[key] = source[key];
            }
        }
        return item;
    }

    /**
     * Get pool statistics
     */
    getStats(): { spawnerCount: number; totalObjects: number } | null {
        return PoolManager.getPoolStats(this._poolType);
    }

    /**
     * Clear the pool
     */
    clear(): void {
        PoolManager.clear(this._poolType);
    }

    /**
     * Destroy the pool
     */
    destroy(): void {
        PoolManager.destroy(this._poolType);
        this._pool = null;
    }
}
