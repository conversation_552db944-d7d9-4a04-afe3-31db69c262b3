// 存储键值常量
export namespace StorageID {
    export const GameTag: string = "TPDG";
    export const Setting_Data: string = "setting_data";
    export const Model_Name: string = "Name";
    export const UserData: string = "userdata";
    export const TempUser: string = "tempUserId";
    export const LogUserInfo: string = "logUseInfo";
    export const LogServerDomain: string = "logServerDomain";
    export const LastServerId: string = "lastServerId";
    export const BossBattleAutoFight: string = "BossBattleAutoFight";
    export const TempSimulatorData: string = "TempSimulatorData";
    export const TempisFirst: string = "Temp_isFirst";
    export const BadgeShow: string = "BadgeShow";
    export const LoginDay: string = "LoginDay";
    export const KnapsackData: string = "KnapsackData";
    export const MCatGame: string = "MCatGame";
}
