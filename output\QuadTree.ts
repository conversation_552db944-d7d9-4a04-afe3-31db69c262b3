/**
 * QuadTree spatial partitioning data structure
 * Used for efficient collision detection and spatial queries
 */

// Debug colors for different tree depths
const DEBUG_COLORS = [cc.Color.BLACK, cc.Color.RED, cc.Color.BLUE, cc.Color.ORANGE, cc.Color.GREEN];

/**
 * Interface for objects that can be stored in the QuadTree
 */
export interface QuadTreeObject {
    x: number;
    y: number;
    width: number;
    height: number;
    cindex?: number[];
    getAllNeedTestColliders?(result: any[]): void;
}

/**
 * Extended bounds interface with half-width and half-height
 */
export interface QuadTreeBounds extends cc.Rect {
    halfWidth?: number;
    halfHeight?: number;
}

/**
 * QuadTree implementation for spatial partitioning
 */
export class QuadTree {
    public nodes: QuadTree[] | null = null;
    public children: QuadTreeObject[] | null = null;
    private _bounds: QuadTreeBounds;
    private _depth: number = 0;
    private _maxChildren: number = 6;
    private _maxDepth: number = 6;

    /**
     * Create a new QuadTree
     * @param bounds - The bounding rectangle for this tree node
     * @param depth - Current depth in the tree (default: 0)
     * @param maxDepth - Maximum depth allowed (default: 4)
     * @param maxChildren - Maximum children per node before subdivision (default: 10)
     */
    constructor(bounds: QuadTreeBounds, depth: number = 0, maxDepth: number = 4, maxChildren: number = 10) {
        this._bounds = bounds;
        this.children = [];
        this.nodes = [];
        this._maxChildren = maxChildren;
        this._maxDepth = maxDepth;
        this._depth = depth;

        // Calculate half dimensions for optimization
        if (!this._bounds.halfHeight) {
            this._bounds.halfHeight = this._bounds.height / 2;
        }
        if (!this._bounds.halfWidth) {
            this._bounds.halfWidth = this._bounds.width / 2;
        }
    }

    /**
     * Get all objects that need collision testing
     */
    getAllNeedTestColliders(result: any[]): void {
        if (this.children && this.children.length > 0) {
            result.push(this.children);
            for (let i = 0; i < this.children.length; i++) {
                if (this.children[i].cindex) {
                    this.children[i].cindex!.push(result.length - 1);
                }
            }
        }

        if (this.nodes) {
            for (let i = 0; i < this.nodes.length; i++) {
                this.nodes[i].getAllNeedTestColliders(result);
            }
        }
    }

    /**
     * Render the QuadTree for debugging
     */
    render(graphics: cc.Graphics): void {
        // Render child nodes first
        if (this.nodes) {
            for (let i = 0; i < this.nodes.length; i++) {
                const node = this.nodes[i];
                if (node) {
                    node.render(graphics);
                }
            }
        }

        // Draw this node's bounds
        graphics.lineWidth = cc.misc.clampf(8 - this._depth, 2, 8);
        graphics.strokeColor = DEBUG_COLORS[this._depth] || cc.Color.WHITE;
        
        graphics.moveTo(this._bounds.x, this._bounds.y);
        graphics.lineTo(this._bounds.x + this._bounds.width, this._bounds.y);
        graphics.lineTo(this._bounds.x + this._bounds.width, this._bounds.y + this._bounds.height);
        graphics.lineTo(this._bounds.x, this._bounds.y + this._bounds.height);
        graphics.close();
        graphics.stroke();
    }

    /**
     * Insert an object into the QuadTree
     */
    insert(obj: QuadTreeObject): void {
        if (!this.children) {
            this.children = [];
        }

        // If we have subnodes, try to insert into them
        if (this.nodes && this.nodes.length > 0) {
            const index = this.getIndex(obj);
            if (index !== -1) {
                this.nodes[index].insert(obj);
                return;
            }
        }

        // Add to this node
        this.children.push(obj);

        // Check if we need to subdivide
        if (this.children.length > this._maxChildren && this._depth < this._maxDepth) {
            if (!this.nodes || this.nodes.length === 0) {
                this.split();
            }

            // Try to move children to subnodes
            let i = 0;
            while (i < this.children.length) {
                const index = this.getIndex(this.children[i]);
                if (index !== -1) {
                    const child = this.children.splice(i, 1)[0];
                    this.nodes![index].insert(child);
                } else {
                    i++;
                }
            }
        }
    }

    /**
     * Determine which quadrant an object belongs to
     */
    private getIndex(obj: QuadTreeObject): number {
        let index = -1;
        const verticalMidpoint = this._bounds.x + this._bounds.halfWidth!;
        const horizontalMidpoint = this._bounds.y + this._bounds.halfHeight!;

        // Object can completely fit within the top quadrants
        const topQuadrant = (obj.y < horizontalMidpoint && obj.y + obj.height < horizontalMidpoint);
        // Object can completely fit within the bottom quadrants
        const bottomQuadrant = (obj.y > horizontalMidpoint);

        // Object can completely fit within the left quadrants
        if (obj.x < verticalMidpoint && obj.x + obj.width < verticalMidpoint) {
            if (topQuadrant) {
                index = 1; // Top-left
            } else if (bottomQuadrant) {
                index = 2; // Bottom-left
            }
        }
        // Object can completely fit within the right quadrants
        else if (obj.x > verticalMidpoint) {
            if (topQuadrant) {
                index = 0; // Top-right
            } else if (bottomQuadrant) {
                index = 3; // Bottom-right
            }
        }

        return index;
    }

    /**
     * Split the node into four quadrants
     */
    private split(): void {
        const subWidth = this._bounds.halfWidth!;
        const subHeight = this._bounds.halfHeight!;
        const x = this._bounds.x;
        const y = this._bounds.y;

        this.nodes = [
            // Top-right
            new QuadTree(
                cc.rect(x + subWidth, y, subWidth, subHeight),
                this._depth + 1,
                this._maxDepth,
                this._maxChildren
            ),
            // Top-left
            new QuadTree(
                cc.rect(x, y, subWidth, subHeight),
                this._depth + 1,
                this._maxDepth,
                this._maxChildren
            ),
            // Bottom-left
            new QuadTree(
                cc.rect(x, y + subHeight, subWidth, subHeight),
                this._depth + 1,
                this._maxDepth,
                this._maxChildren
            ),
            // Bottom-right
            new QuadTree(
                cc.rect(x + subWidth, y + subHeight, subWidth, subHeight),
                this._depth + 1,
                this._maxDepth,
                this._maxChildren
            )
        ];
    }

    /**
     * Retrieve all objects that could collide with the given object
     */
    retrieve(obj: QuadTreeObject): QuadTreeObject[] {
        const returnObjects: QuadTreeObject[] = [];
        const index = this.getIndex(obj);

        // If we have subnodes and the object fits in one
        if (this.nodes && this.nodes.length > 0 && index !== -1) {
            returnObjects.push(...this.nodes[index].retrieve(obj));
        }

        // Add all objects at this level
        if (this.children) {
            returnObjects.push(...this.children);
        }

        return returnObjects;
    }

    /**
     * Clear the QuadTree
     */
    clear(): void {
        if (this.children) {
            this.children.length = 0;
        }

        if (this.nodes) {
            for (let i = 0; i < this.nodes.length; i++) {
                if (this.nodes[i]) {
                    this.nodes[i].clear();
                }
            }
            this.nodes.length = 0;
        }
    }

    /**
     * Get the bounds of this QuadTree node
     */
    get bounds(): QuadTreeBounds {
        return this._bounds;
    }

    /**
     * Get the current depth of this node
     */
    get depth(): number {
        return this._depth;
    }

    /**
     * Get the number of objects in this node (not including subnodes)
     */
    get objectCount(): number {
        return this.children ? this.children.length : 0;
    }

    /**
     * Get total number of objects in this node and all subnodes
     */
    getTotalObjectCount(): number {
        let count = this.objectCount;
        
        if (this.nodes) {
            for (const node of this.nodes) {
                count += node.getTotalObjectCount();
            }
        }
        
        return count;
    }

    /**
     * Check if this node has been subdivided
     */
    get hasSubnodes(): boolean {
        return this.nodes !== null && this.nodes.length > 0;
    }
}
