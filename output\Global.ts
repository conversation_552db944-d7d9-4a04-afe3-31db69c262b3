// Global utility functions

/**
 * Check if a value is null or empty
 * @param value - The value to check
 * @returns true if the value is null, undefined, or empty string
 */
export function isNullOrEmpty(value: any): boolean {
    if (value == null) {
        return true;
    }
    if (value === "") {
        return true;
    }
    const type = typeof value;
    if (type !== "string" && type !== "number") {
        cc.error("isNullOrEmpty error type", value);
    }
    return false;
}

/**
 * Create a shallow copy of an object or array
 * @param source - The source object or array to copy
 * @returns A shallow copy of the source
 */
export function copy<T>(source: T): T {
    if (source === null || typeof source !== "object") {
        return source;
    }
    
    const result = Array.isArray(source) ? [] : {} as any;
    Object.keys(source).forEach((key) => {
        result[key] = (source as any)[key];
    });
    return result;
}

/**
 * Convert an object to an array of its values
 * @param obj - The object to convert
 * @returns An array containing all values from the object
 */
export function toArray<T>(obj: Record<string, T>): T[] {
    const result: T[] = [];
    if (obj === null || typeof obj !== "object") {
        return result;
    }
    
    for (const key in obj) {
        const value = obj[key];
        result.push(value);
    }
    return result;
}

/**
 * Create a deep copy of an object with circular reference handling
 * @param source - The source object to deep copy
 * @returns A deep copy of the source object
 */
export function GameDeepCopy<T>(source: T): T {
    const visited: Array<{ target: any; copyTarget: any }> = [];
    
    function deepCopyInternal(obj: any): any {
        if (typeof obj !== "object" || !obj) {
            return obj;
        }
        
        // Check for circular references
        for (let i = 0; i < visited.length; i++) {
            if (visited[i].target === obj) {
                return visited[i].copyTarget;
            }
        }
        
        const result = Array.isArray(obj) ? [] : {};
        visited.push({
            target: obj,
            copyTarget: result
        });
        
        Object.keys(obj).forEach((key) => {
            if (!(result as any)[key]) {
                (result as any)[key] = deepCopyInternal(obj[key]);
            }
        });
        
        return result;
    }
    
    return deepCopyInternal(source);
}

// For backward compatibility, also attach to window if in browser environment
declare global {
    interface Window {
        isNullOrEmpty: typeof isNullOrEmpty;
        copy: typeof copy;
        toArray: typeof toArray;
        GameDeepCopy: typeof GameDeepCopy;
    }
}

if (typeof window !== "undefined") {
    window.isNullOrEmpty = isNullOrEmpty;
    window.copy = copy;
    window.toArray = toArray;
    window.GameDeepCopy = GameDeepCopy;
}
