import { AudioManager } from "./AudioManager";
import { LoaderAdapter } from "./LoaderAdapter";
import { VoManager } from "./VoManager";
import { StorageManager } from "./StorageManager";
import { UIManager } from "./UIManager";
import { NetAdapter } from "./NetAdapter";
import GuidesModel from "./GuidesModel";
import ShopModel from "./ShopModel";
import { Notifier } from "./Notifier";
import { CallID } from "./CallID";
import ADModel from "./ADModel";
import Random from "./Random";

/**
 * Main manager class that provides access to all game subsystems
 * Acts as a central hub for accessing various managers and services
 */
class ManagerClass {
    private _storage: StorageManager | null = null;
    private _audio: AudioManager | null = null;
    
    public oldGroupMatrix: string;
    public random: Random;

    constructor() {
        // Store original collision matrix for restoration
        this.oldGroupMatrix = JSON.stringify(cc.game.collisionMatrix);
        this.random = new Random();
    }

    /**
     * Get loader adapter for asset loading
     */
    get loader(): typeof LoaderAdapter {
        return LoaderAdapter;
    }

    /**
     * Get guides model instance
     */
    get guide(): GuidesModel {
        return GuidesModel.instance;
    }

    /**
     * Get storage manager instance (lazy initialization)
     */
    get storage(): StorageManager {
        if (this._storage == null) {
            this._storage = new StorageManager();
        }
        return this._storage;
    }

    /**
     * Get audio manager instance (lazy initialization)
     */
    get audio(): AudioManager {
        if (this._audio == null) {
            this._audio = new AudioManager();
        }
        return this._audio;
    }

    /**
     * Get network adapter instance
     */
    get net(): typeof NetAdapter {
        return NetAdapter;
    }

    /**
     * Get VO (Value Object) manager instance
     */
    get vo(): VoManager {
        return VoManager.instance;
    }

    /**
     * Get UI manager instance
     */
    get ui(): UIManager {
        return UIManager.instance;
    }

    /**
     * Get shop model instance
     */
    get Shop(): ShopModel {
        return ShopModel.instance;
    }

    /**
     * Get advertisement model instance
     */
    get AD(): ADModel {
        return ADModel.instance;
    }

    /**
     * Get level manager through notifier call
     */
    get leveMgr(): any {
        return Notifier.call(CallID.Game_GetLeverVo);
    }

    /**
     * Set physics configuration (override in implementation)
     */
    setPhysics(): void {
        // Implementation specific to physics setup
    }

    /**
     * Get collision group index by name
     * @param groupName - Name of the collision group
     * @returns Group index or -1 if not found
     */
    getGroupIndex(groupName: string): number {
        return cc.game.groupList.indexOf(groupName);
    }

    /**
     * Set collision matrix value by indices
     * @param groupIndex1 - First group index
     * @param groupIndex2 - Second group index
     * @param value - Collision value (true/false)
     */
    setGroupMatrix(groupIndex1: number, groupIndex2: number, value: boolean): void {
        try {
            cc.game.collisionMatrix[groupIndex1][groupIndex2] = value;
        } catch (error) {
            cc.error("[setGroupMatrix]失败", error);
        }
    }

    /**
     * Set collision matrix value by group names
     * @param groupName1 - First group name
     * @param groupName2 - Second group name
     * @param value - Collision value (true/false)
     */
    setGroupMatrixByStr(groupName1: string, groupName2: string, value: boolean): void {
        try {
            const index1 = this.getGroupIndex(groupName1);
            const index2 = this.getGroupIndex(groupName2);
            cc.game.collisionMatrix[index1][index2] = value;
        } catch (error) {
            cc.error("[setGroupMatrix]失败", error);
        }
    }

    /**
     * Restore collision matrix to original state
     */
    restoreGroupMatrix(): void {
        cc.game.collisionMatrix = JSON.parse(this.oldGroupMatrix);
    }

    /**
     * Initialize all managers
     */
    init(): void {
        // Initialize core managers
        this.storage; // Trigger lazy initialization
        this.audio;   // Trigger lazy initialization
        
        // Set up physics if needed
        this.setPhysics();
    }

    /**
     * Update all managers (call from main game loop)
     * @param deltaTime - Time since last update
     */
    update(deltaTime: number): void {
        // Update managers that need frame updates
        if (this._audio) {
            this._audio.update?.(deltaTime);
        }
    }

    /**
     * Cleanup all managers
     */
    destroy(): void {
        if (this._storage) {
            this._storage.destroy?.();
            this._storage = null;
        }
        
        if (this._audio) {
            this._audio.destroy?.();
            this._audio = null;
        }

        // Restore collision matrix
        this.restoreGroupMatrix();
    }

    /**
     * Get all active managers for debugging
     */
    getActiveManagers(): { [key: string]: any } {
        return {
            storage: this._storage,
            audio: this._audio,
            loader: this.loader,
            guide: this.guide,
            net: this.net,
            vo: this.vo,
            ui: this.ui,
            shop: this.Shop,
            ad: this.AD
        };
    }

    /**
     * Check if a manager is initialized
     * @param managerName - Name of the manager to check
     * @returns True if manager is initialized
     */
    isManagerInitialized(managerName: string): boolean {
        switch (managerName) {
            case "storage":
                return this._storage != null;
            case "audio":
                return this._audio != null;
            default:
                return false;
        }
    }
}

// Export singleton instance
export const Manager = new ManagerClass();
