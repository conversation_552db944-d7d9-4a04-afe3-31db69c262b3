/**
 * Seeded random number generator
 * Provides deterministic random numbers based on a seed value
 */
export default class Random {
    private readonly iRandomMax: number = 200000000000; // 2e11
    private _seed: number = 0;

    constructor(seed?: number) {
        if (seed !== undefined) {
            this._seed = seed;
        }
    }

    /**
     * Get the current seed value
     */
    get seed(): number {
        return this._seed;
    }

    /**
     * Set the seed value
     */
    set seed(value: number) {
        this._seed = value;
    }

    /**
     * Generate a random float between 0 and 1
     * @returns Random float [0, 1)
     */
    random(): number {
        this._seed = (9301 * this._seed + 49297) % 233280;
        return this._seed / 233280;
    }

    /**
     * Generate a random integer between min and max (inclusive)
     * @param min - Minimum value (or max if only one parameter)
     * @param max - Maximum value (optional)
     * @returns Random integer
     */
    randomInt(min: number, max?: number): number {
        if (max === undefined) {
            max = min;
            min = 0;
        }
        const value = min + this.random() * (max - min);
        return Math.round(value);
    }

    /**
     * Generate a random double between min and max
     * @param min - Minimum value (or max if only one parameter)
     * @param max - Maximum value (optional)
     * @returns Random double
     */
    randomDouble(min: number, max?: number): number {
        if (max === undefined) {
            max = min;
            min = 0;
        }
        return min + this.random() * (max - min);
    }

    /**
     * Generate a random number within a range
     * @param range - The range limit
     * @returns Random number [0, range)
     */
    randomRange(range: number): number {
        return this.randomInt(0, this.iRandomMax) % range;
    }

    /**
     * Test random odds
     * @param total - Total possible outcomes
     * @param success - Number of successful outcomes
     * @returns 1 if successful, 0 if not
     */
    randomOdds(total: number, success: number): number {
        if (this.randomRange(total) < success) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * Get random elements from array without duplicates
     * Modifies the original array by shuffling and truncating
     * @param array - Source array to select from
     * @param count - Number of elements to select
     * @returns The modified array with selected elements
     */
    getRandomSDiffInArray<T>(array: T[], count: number): T[] {
        const length = array.length;
        
        if (array.length < count) {
            return array;
        }
        
        if (array.length === 1) {
            return array;
        }

        // Fisher-Yates shuffle for first 'count' elements
        for (let i = 0; i < count; i++) {
            const randomIndex = this.randomInt(i, length - 1);
            [array[i], array[randomIndex]] = [array[randomIndex], array[i]];
        }

        // Truncate array to desired count
        array.length = count;
        return array;
    }

    /**
     * Get random elements from array without modifying original
     * @param array - Source array to select from
     * @param count - Number of elements to select
     * @returns New array with selected elements
     */
    getRandomElements<T>(array: T[], count: number): T[] {
        const copy = [...array];
        return this.getRandomSDiffInArray(copy, count);
    }

    /**
     * Shuffle an array in place
     * @param array - Array to shuffle
     * @returns The shuffled array
     */
    shuffle<T>(array: T[]): T[] {
        for (let i = array.length - 1; i > 0; i--) {
            const j = this.randomInt(0, i);
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    /**
     * Get a random element from an array
     * @param array - Source array
     * @returns Random element from the array
     */
    choice<T>(array: T[]): T | undefined {
        if (array.length === 0) return undefined;
        const index = this.randomInt(0, array.length - 1);
        return array[index];
    }

    /**
     * Generate random boolean with given probability
     * @param probability - Probability of true (0-1)
     * @returns Random boolean
     */
    randomBool(probability: number = 0.5): boolean {
        return this.random() < probability;
    }

    /**
     * Generate random number with normal distribution (Box-Muller transform)
     * @param mean - Mean value
     * @param stdDev - Standard deviation
     * @returns Random number with normal distribution
     */
    randomNormal(mean: number = 0, stdDev: number = 1): number {
        // Box-Muller transform
        const u1 = this.random();
        const u2 = this.random();
        const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
        return z0 * stdDev + mean;
    }

    /**
     * Reset the random generator with a new seed
     * @param seed - New seed value
     */
    reset(seed: number): void {
        this._seed = seed;
    }

    /**
     * Create a new Random instance with the same seed
     * @returns New Random instance
     */
    clone(): Random {
        const newRandom = new Random();
        newRandom._seed = this._seed;
        return newRandom;
    }
}
