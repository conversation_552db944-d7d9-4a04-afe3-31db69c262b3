export enum StorageID {
    GameTag = "TPDG",
    Setting_Data = "setting_data",
    Model_Name = "Name",
    UserData = "userdata",
    TempUser = "tempUserId",
    LogUserInfo = "logUseInfo",
    LogServerDomain = "logServerDomain",
    LastServerId = "lastServerId",
    BossBattleAutoFight = "BossBattleAutoFight",
    TempSimulatorData = "TempSimulatorData",
    TempisFirst = "Temp_isFirst",
    BadgeShow = "BadgeShow",
    LoginDay = "LoginDay",
    KnapsackData = "KnapsackData",
    MCatGame = "MCatGame"
}
