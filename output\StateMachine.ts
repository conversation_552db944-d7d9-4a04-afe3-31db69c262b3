import { GameUtil } from "./GameUtil";

/**
 * State types enumeration
 */
export enum StateType {
    NONE = -1,
    IDLE = 0,
    MOVE = 1,
    ATTACK = 2,
    GLOBAL = 3,
    BEHIT = 4,
    DEAD = 5,
    SPRINT = 6,
    ESCAPE = 7,
    SKILL = 8,
    WANDER = 9,
    FOLLOW = 10,
    THINK = 11,
    HIDE = 12,
    APPEAR = 13
}

/**
 * State transition interface
 */
export interface StateTransition {
    fromTag: StateType;
    toTag: StateType;
    onCheck: (owner: any) => boolean;
    onCompletaCallBack: (owner: any) => boolean;
}

/**
 * State interface
 */
export interface IState {
    tag: StateType;
    onEnter?: (owner: any) => void;
    onUpdate?: (owner: any, deltaTime: number) => void;
    onExit?: (owner: any) => void;
}

/**
 * Entity interface for state machine
 */
export interface StateEntity {
    steering?: {
        calculate(): cc.Vec2;
    };
    velocity: cc.Vec2;
    position: cc.Vec2;
    heading: cc.Vec2;
    maxSpeed: number;
    enforceNonPeretration(pos: cc.Vec2): void;
    setPosition(pos: cc.Vec2): void;
    updateDir(deltaTime: number): void;
}

/**
 * State machine implementation
 */
export class State {
    static Type = StateType;

    private _owner: any;
    private _isReEnter: boolean = true;
    private _transitionMap: { [key: number]: StateTransition[] } = {};
    private _stateMap: { [key: number]: IState } = {};
    private _curSubStateTag: StateType = StateType.NONE;
    private _preSubStateTag: StateType = StateType.NONE;
    private _parentStateTag: StateType = StateType.NONE;
    private _parentState?: State;
    private _duration: number = 0;
    
    public tag: StateType = StateType.NONE;
    public moveLimtX?: [number, number];
    public moveLimtY?: [number, number];

    // Temporary vector for calculations
    private static tempVec = cc.v2();

    constructor(owner: any, isReEnter: boolean = true) {
        this._owner = owner;
        this.isReEnter = isReEnter;
    }

    /**
     * Get current state tag
     */
    get curStateTag(): StateType {
        return this.tag;
    }

    /**
     * Get current sub-state tag
     */
    get curSubStateTag(): StateType {
        return this._curSubStateTag;
    }

    /**
     * Get/Set re-enter flag
     */
    get isReEnter(): boolean {
        return this._isReEnter;
    }

    set isReEnter(value: boolean) {
        this._isReEnter = value;
    }

    /**
     * Set parent state
     * @param parentState - Parent state machine
     */
    setParentState(parentState: State): void {
        this._parentState = parentState;
    }

    /**
     * Add state transition
     * @param transition - State transition configuration
     * @returns True if added successfully
     */
    addTransition(transition: StateTransition): boolean {
        let transitions = this._transitionMap[transition.fromTag];
        
        if (transitions) {
            // Check for duplicate transition
            for (const existing of transitions) {
                if (existing.fromTag === transition.fromTag && existing.toTag === transition.toTag) {
                    return false;
                }
            }
            transitions.push(transition);
        } else {
            this._transitionMap[transition.fromTag] = [transition];
        }
        
        return true;
    }

    /**
     * Get transition between two states
     * @param fromTag - Source state
     * @param toTag - Target state
     * @returns Transition or null
     */
    getTransition(fromTag: StateType, toTag: StateType): StateTransition | null {
        const transitions = this._transitionMap[fromTag];
        if (transitions) {
            for (const transition of transitions) {
                if (transition.fromTag === fromTag && transition.toTag === toTag) {
                    return transition;
                }
            }
        }
        return null;
    }

    /**
     * Check and execute state transitions
     */
    checkTransition(): void {
        let nextState = StateType.NONE;
        const transitions = this._transitionMap[this._curSubStateTag];
        
        if (transitions) {
            for (const transition of transitions) {
                if (transition.onCheck(this._owner) && transition.onCompletaCallBack(this._owner)) {
                    nextState = transition.toTag;
                    break;
                }
            }
        }
        
        if (nextState !== StateType.NONE) {
            this.changeState(nextState);
        }
    }

    /**
     * Steering movement with basic physics
     * @param entity - Entity to move
     * @param deltaTime - Time delta
     */
    onSteerMove(entity: StateEntity, deltaTime: number): void {
        if (!entity.steering) return;

        const force = entity.steering.calculate();
        const tempVec = State.tempVec;
        
        // Apply force to velocity
        cc.Vec2.multiplyScalar(tempVec, force, deltaTime);
        cc.Vec2.add(tempVec, tempVec, entity.velocity);
        GameUtil.Vec2Truncate(tempVec, entity.maxSpeed);
        entity.velocity.set(tempVec);
        
        // Update position
        cc.Vec2.multiplyScalar(tempVec, entity.velocity, deltaTime);
        cc.Vec2.add(tempVec, tempVec, entity.position);
        entity.enforceNonPeretration(tempVec);
        entity.setPosition(tempVec);
        
        // Update heading
        if (entity.velocity.magSqr() > 1e-5) {
            cc.Vec2.normalize(tempVec, entity.velocity);
            entity.heading = tempVec;
        }
        
        entity.updateDir(deltaTime);
    }

    /**
     * Steering movement with movement limits
     * @param entity - Entity to move
     * @param deltaTime - Time delta
     */
    onSteMove(entity: StateEntity, deltaTime: number): void {
        if (!entity.steering) return;

        const force = entity.steering.calculate();
        const tempVec = State.tempVec;
        
        // Apply force to velocity
        cc.Vec2.multiplyScalar(tempVec, force, deltaTime);
        cc.Vec2.add(tempVec, tempVec, entity.velocity);
        GameUtil.Vec2Truncate(tempVec, entity.maxSpeed);
        entity.velocity.set(tempVec);
        
        // Update position with limits
        cc.Vec2.multiplyScalar(tempVec, entity.velocity, deltaTime);
        cc.Vec2.add(tempVec, tempVec, entity.position);
        entity.enforceNonPeretration(tempVec);
        
        // Apply movement limits
        if (this.moveLimtX) {
            tempVec.x = cc.misc.clampf(tempVec.x, this.moveLimtX[0], this.moveLimtX[1]);
        }
        if (this.moveLimtY) {
            tempVec.y = cc.misc.clampf(tempVec.y, this.moveLimtY[0], this.moveLimtY[1]);
        }
        
        entity.setPosition(tempVec);
        
        // Update heading
        if (entity.velocity.magSqr() > 1e-5) {
            cc.Vec2.normalize(tempVec, entity.velocity);
            entity.heading = tempVec;
        }
        
        entity.updateDir(deltaTime);
    }

    /**
     * Set movement limits
     * @param limitX - X-axis limits [min, max]
     * @param limitY - Y-axis limits [min, max]
     */
    setMoveLimt(limitX?: [number, number], limitY?: [number, number]): void {
        this.moveLimtX = limitX;
        this.moveLimtY = limitY;
    }

    /**
     * Add state to the state machine
     * @param state - State to add
     */
    addState(state: IState): void {
        this._stateMap[state.tag] = state;
    }

    /**
     * Change to a new state
     * @param stateTag - Target state tag
     * @returns True if state changed successfully
     */
    changeState(stateTag: StateType): boolean {
        // Check if state exists
        const newState = this._stateMap[stateTag];
        if (!newState) {
            return false;
        }

        // Check if already in this state and re-enter is disabled
        if (this._curSubStateTag === stateTag && !this._isReEnter) {
            return false;
        }

        // Exit current state
        const currentState = this._stateMap[this._curSubStateTag];
        if (currentState && currentState.onExit) {
            currentState.onExit(this._owner);
        }

        // Update state tags
        this._preSubStateTag = this._curSubStateTag;
        this._curSubStateTag = stateTag;
        this._duration = 0;

        // Enter new state
        if (newState.onEnter) {
            newState.onEnter(this._owner);
        }

        return true;
    }

    /**
     * Update the state machine
     * @param deltaTime - Time delta
     */
    update(deltaTime: number): void {
        this._duration += deltaTime;
        
        // Update current state
        const currentState = this._stateMap[this._curSubStateTag];
        if (currentState && currentState.onUpdate) {
            currentState.onUpdate(this._owner, deltaTime);
        }

        // Check for transitions
        this.checkTransition();
    }

    /**
     * Get state duration
     */
    get duration(): number {
        return this._duration;
    }

    /**
     * Get previous state tag
     */
    get preSubStateTag(): StateType {
        return this._preSubStateTag;
    }

    /**
     * Check if in specific state
     * @param stateTag - State to check
     * @returns True if in the specified state
     */
    isInState(stateTag: StateType): boolean {
        return this._curSubStateTag === stateTag;
    }

    /**
     * Force set state without transitions
     * @param stateTag - State to set
     */
    forceSetState(stateTag: StateType): void {
        this._curSubStateTag = stateTag;
        this._duration = 0;
    }
}
