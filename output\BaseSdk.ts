/**
 * Video advertisement result codes
 */
export enum VideoAdCode {
    COMPLETE = 0,
    NOT_SUPPORT = 1,
    NOT_READY = 2,
    UNKNOW_AdId = 3,
    NOT_COMPLITE = 4,
    AD_ERROR = 5,
    SHOW_SUCCESS = 6
}

/**
 * Share types for different sharing scenarios
 */
export enum ShareType {
    SHARE_CHALLENGE = 1,
    SHARE_GROUP = 2,
    SHARE_NORMAL = 3,
    SHARE_REWARD = 4,
    SHARE_VICTORY = 5,
    SHARE_SORCE = 6,
    SHARE_RANK = 7,
    SHARE_HELP = 8,
    SHARE_OTHER = 9
}

/**
 * Banner style configuration interface
 */
export interface BannerStyle {
    left?: number;
    top?: number;
    width?: number;
    height?: number;
}

/**
 * User information interface
 */
export interface UserInfo {
    nickName?: string;
    avatarUrl?: string;
    gender?: number;
    country?: string;
    province?: string;
    city?: string;
    language?: string;
}

/**
 * BMS (Backend Management System) value object interface
 */
export interface BmsVo {
    ip?: string;
    [key: string]: any;
}

/**
 * Native ad information interface
 */
export interface NativeAdInfo {
    title?: string;
    desc?: string;
    iconUrl?: string;
    imageUrl?: string;
    clickBtnTxt?: string;
    creativeType?: number;
    interactionType?: number;
    logoUrl?: string;
    [key: string]: any;
}

/**
 * Base SDK class providing common platform functionality
 * This class should be extended by platform-specific SDK implementations
 */
export class BaseSdk {
    protected _shareList: any[] = [];
    protected _appId: string = "";
    protected _openId: string = "";
    public bmsVo: BmsVo | null = null;
    public isIpEnable: number = 1;
    public ip: number = 0;

    /**
     * Set super properties for analytics (override in subclasses)
     */
    setSuperProperties(): void {
        // Override in platform-specific implementations
    }

    /**
     * Set user properties for analytics (override in subclasses)
     */
    setUserProperties(): void {
        // Override in platform-specific implementations
    }

    /**
     * Initialize the SDK
     * @param appId - Application ID
     */
    init(appId?: string): void {
        cc.game.on("exit_game", this.exitGame, this);
        if (appId) {
            this.setAppId(appId);
        }
    }

    /**
     * Handle game exit
     */
    exitGame(): void {
        cc.game.end();
    }

    /**
     * Show banner advertisement
     * @param adId - Advertisement ID
     * @param callback - Callback function
     */
    showBanner(adId: string, callback?: (success: boolean) => void): void {
        this.showBannerWithStyle(adId, {}, callback);
    }

    /**
     * Show banner advertisement with custom style
     * @param adId - Advertisement ID
     * @param style - Banner style configuration
     * @param callback - Callback function
     */
    showBannerWithStyle(adId: string, style: BannerStyle, callback?: (success: boolean) => void): void {
        // Override in platform-specific implementations
    }

    /**
     * Show full screen video advertisement
     */
    showFullVideoAD(): void {
        // Override in platform-specific implementations
    }

    /**
     * Show interstitial advertisement
     */
    showInsertAd(): void {
        // Override in platform-specific implementations
    }

    /**
     * Show splash advertisement
     */
    showSplashAd(): void {
        // Override in platform-specific implementations
    }

    /**
     * Show feed advertisement
     */
    showFeedAd(): void {
        // Override in platform-specific implementations
    }

    /**
     * Hide feed advertisement
     */
    hideFeedAd(): void {
        // Override in platform-specific implementations
    }

    /**
     * Show privacy policy
     * @param callback - Callback function with user consent result
     */
    showPrivacy(callback?: (agreed: boolean) => void): void {
        if (callback) {
            callback(true);
        }
    }

    /**
     * Get user information
     * @param callback - Callback function with user info
     * @returns Promise with user info
     */
    getUserInfo(callback?: (userInfo: UserInfo | null) => void): Promise<UserInfo | null> {
        return new Promise<UserInfo | null>((resolve) => {
            resolve(null);
            if (callback) {
                callback(null);
            }
        });
    }

    /**
     * Set share list configuration
     */
    setShareList(): void {
        // Override in platform-specific implementations
    }

    /**
     * Get application ID
     * @returns Application ID
     */
    getAppId(): string {
        return this._appId;
    }

    /**
     * Set application ID
     * @param appId - Application ID
     * @returns This instance for chaining
     */
    setAppId(appId: string): this {
        this._appId = appId;
        return this;
    }

    /**
     * Get open ID
     * @returns Open ID
     */
    getOpenId(): string {
        return this._openId;
    }

    /**
     * Set open ID
     * @param openId - Open ID
     * @returns This instance for chaining
     */
    setOpenId(openId: string): this {
        this._openId = openId;
        return this;
    }

    /**
     * Set BMS value object
     * @param bmsVo - BMS value object
     */
    setBmsVo(bmsVo: BmsVo): void {
        this.bmsVo = bmsVo;
        if (bmsVo.ip) {
            this.ip = parseInt(bmsVo.ip) || 0;
        }
    }

    /**
     * Set IP enable status
     * @param enable - Enable status
     */
    setIpEnable(enable: number): void {
        this.isIpEnable = enable;
    }

    /**
     * Trigger device vibration
     * @param type - Vibration type (0 = short, 1 = long)
     */
    vibrate(type: number = 0): void {
        // Override in platform-specific implementations
    }

    /**
     * Create app recommendation box
     */
    createAppBox(): void {
        // Override in platform-specific implementations
    }

    /**
     * Show app recommendation box
     */
    showAppBox(): void {
        // Override in platform-specific implementations
    }

    /**
     * Preload reward video advertisement
     */
    preLoadRewardVideo(): void {
        // Override in platform-specific implementations
    }

    /**
     * Navigate to app rating page
     */
    goRate(): void {
        // Override in platform-specific implementations
    }

    /**
     * Set login finish status
     */
    setLoginFinish(): void {
        // Override in platform-specific implementations
    }

    /**
     * Navigate to payment page
     */
    toPay(): void {
        // Override in platform-specific implementations
    }

    /**
     * Restore previous purchases
     */
    toRestorePay(): void {
        // Override in platform-specific implementations
    }

    /**
     * Get native advertisement information
     * @returns Native ad info or null
     */
    getNativeAdInfo(): NativeAdInfo | null {
        return null;
    }

    /**
     * Show native full screen video advertisement
     */
    showNativeFullVideoAD(): void {
        // Override in platform-specific implementations
    }

    /**
     * Refresh native advertisement
     */
    nativeAdRefresh(): void {
        // Override in platform-specific implementations
    }

    /**
     * Share to Facebook
     */
    toShareFaceBook(): void {
        // Override in platform-specific implementations
    }

    /**
     * Notify role creation for analytics
     */
    notifyCreateRole(): void {
        // Override in platform-specific implementations
    }

    /**
     * Notify role entering game for analytics
     */
    notifyRoleEnterGame(): void {
        // Override in platform-specific implementations
    }

    /**
     * Notify role level up for analytics
     */
    notifyRoleLevelUp(): void {
        // Override in platform-specific implementations
    }

    /**
     * Check content for compliance
     * @returns Promise with check result
     */
    checkContent(): Promise<boolean> {
        return Promise.resolve(true);
    }

    /**
     * Get item from local storage
     * @param key - Storage key
     * @returns Stored value or null
     */
    getStorageItem(key: string): string | null {
        return cc.sys.localStorage.getItem(key);
    }

    /**
     * Set item in local storage
     * @param key - Storage key
     * @param value - Value to store
     */
    setStorageItem(key: string, value: string): void {
        cc.sys.localStorage.setItem(key, value);
    }

    /**
     * Clear all local storage
     */
    clearStorage(): void {
        cc.sys.localStorage.clear();
    }

    /**
     * Remove item from local storage
     * @param key - Storage key
     */
    removeStorageItem(key: string): void {
        cc.sys.localStorage.removeItem(key);
    }
}
