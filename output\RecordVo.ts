import { StorageID } from "./StorageID";
import { Notifier } from "./Notifier";
import { NotifyID } from "./NotifyID";
import { Manager } from "./Manager";
import { Time } from "./Time";
import { GameUtil } from "./GameUtil";

/**
 * Configuration options for RecordVo.Mgr
 */
export interface RecordVoOptions {
    isAutoSave?: boolean;
    isAutoToform?: boolean;
    extraDailyKey?: string[];
    onChange?: (vo: any) => void;
}

/**
 * Default data structure for records
 */
export interface DefaultData {
    dailyData: {
        curday: number;
    };
    expireData: {
        time: number;
    };
    [key: string]: any;
}

/**
 * Change handler function type
 */
export type ChangeHandler = (property: string | symbol, value: any) => void;

/**
 * Data factory function type
 */
export type DataFactory<T> = () => T;

/**
 * RecordVo namespace containing data management utilities
 */
export namespace RecordVo {
    /**
     * Default data structure factory
     */
    export function Data(): DefaultData {
        return {
            dailyData: {
                curday: new Date(Date.now()).getDate()
            },
            expireData: {
                time: 0
            }
        };
    }

    /**
     * Generate storage key for a record
     * @param name - Record name
     * @returns Storage key
     */
    function getStorageKey(name: string): string {
        return `${StorageID.GameTag}-Record-${name}`;
    }

    /**
     * Read data from storage
     * @param name - Record name
     * @returns Parsed data or null
     */
    export function ReadData<T = any>(name: string): T | null {
        const dataString = Manager.storage.getString(getStorageKey(name));
        if (dataString) {
            try {
                return JSON.parse(dataString);
            } catch (error) {
                console.error("Failed to parse record data:", error);
                return null;
            }
        }
        return null;
    }

    /**
     * Clean data from storage
     * @param name - Record name
     */
    export function CleanData(name: string): void {
        Manager.storage.remove(getStorageKey(name));
    }

    /**
     * Save data to storage
     * @param name - Record name
     * @param data - Data to save
     */
    export function SaveData(name: string, data: string): void {
        Manager.storage.setString(getStorageKey(name), data);
    }

    /**
     * Create observable proxy for object
     * @param obj - Object to make observable
     * @param handler - Change handler
     * @returns Proxied object
     */
    function createObservable<T>(obj: T, handler: ChangeHandler): T {
        if (obj && typeof obj === "object") {
            return new Proxy(obj, {
                set(target: any, property: string | symbol, value: any, receiver: any): boolean {
                    const result = Reflect.set(target, property, value, receiver);
                    handler(property, value);
                    return result;
                },
                deleteProperty(target: any, property: string | symbol): boolean {
                    const result = Reflect.deleteProperty(target, property);
                    if (result) {
                        handler(property, undefined);
                    }
                    return result;
                }
            });
        }
        return obj;
    }

    /**
     * Create deep observable object
     * @param obj - Object to make observable
     * @param handler - Change handler
     * @returns Deep observable object
     */
    function createDeepObservable<T>(obj: T, handler: ChangeHandler): T {
        if (typeof obj !== "object" || obj == null) {
            throw new Error("The observable must be an object.");
        }

        const result: any = Array.isArray(obj) ? [] : {};
        
        Object.keys(obj as any).forEach(key => {
            const value = (obj as any)[key];
            if (value instanceof Object) {
                Object.keys(value).forEach(subKey => {
                    value[subKey] = createObservable(value[subKey], handler);
                });
            }
            result[key] = createObservable(value, handler);
        });

        return createObservable(result, handler);
    }

    /**
     * Record manager class for handling persistent data with auto-save and daily reset
     */
    export class Mgr<T extends DefaultData = DefaultData> {
        public name: string = "NotDefine";
        public isAutoSave: boolean = false;
        public isSendMsg: boolean = true;
        public isAutoToform: boolean = true;
        public extraDailyKey: string[] = [];
        public vo: T;
        
        private onChangeCall?: (vo: T) => void;
        private getNewDataCall: DataFactory<T>;
        private defaultData: T;
        private sTimer?: number;

        /**
         * Create a new record manager
         * @param name - Record name
         * @param dataFactory - Function to create default data
         * @param options - Configuration options
         */
        constructor(name: string, dataFactory: DataFactory<T>, options: RecordVoOptions = {}) {
            this.name = name;
            this.getNewDataCall = dataFactory;
            this.isAutoSave = options.isAutoSave || false;
            this.isAutoToform = options.isAutoToform !== false; // Default true
            this.extraDailyKey = options.extraDailyKey || [];
            this.onChangeCall = options.onChange;

            this.defaultData = dataFactory();
            
            // Load existing data or use default
            let loadedData = this.ReadData();
            if (loadedData) {
                // Merge with default data to ensure all properties exist
                for (const key in this.defaultData) {
                    if (loadedData[key] == null) {
                        loadedData[key] = this.defaultData[key];
                    }
                }

                if (this.isAutoToform) {
                    loadedData = this.toform(loadedData);
                }

                this.vo = this.isAutoSave 
                    ? createDeepObservable(loadedData, this.onVoChange.bind(this))
                    : GameUtil.deepCopy(loadedData);
                
                this.checkDailyData(this.defaultData);
            } else {
                if (this.isAutoSave) {
                    this.SaveData();
                }
                
                this.vo = this.isAutoSave 
                    ? createDeepObservable(this.defaultData, this.onVoChange.bind(this))
                    : GameUtil.deepCopy(this.defaultData);
            }

            // Trigger initial change callback
            if (this.onChangeCall) {
                this.onChangeCall(this.vo);
            }

            // Listen for new day events
            Notifier.addListener(NotifyID.Time_NewDay, this.onNewDay, this);
        }

        /**
         * Handle value changes in the VO
         */
        private onVoChange(): void {
            if (this.onChangeCall) {
                this.onChangeCall(this.vo);
            }
            if (this.isAutoSave) {
                this.SaveData();
            }
        }

        /**
         * Handle new day event
         */
        private onNewDay(): void {
            this.defaultData = this.getNewDataCall();
            this.checkDailyData(this.defaultData);
        }

        /**
         * Test daily data reset (for debugging)
         */
        testDailyData(): void {
            this.vo.dailyData.curday -= 2;
            this.checkDailyData(this.defaultData);
        }

        /**
         * Check and reset daily data if needed
         * @param defaultData - Default data to use for reset
         */
        private checkDailyData(defaultData: T): void {
            const currentDay = new Date(Date.now()).getDate();
            const dailyKeys = ["dailyData", ...this.extraDailyKey];
            const isNewDay = this.vo.dailyData.curday !== currentDay;

            dailyKeys.forEach(key => {
                const defaultValue = (defaultData as any)[key];
                if (isNewDay) {
                    (this.vo as any)[key] = createDeepObservable(defaultValue, this.onVoChange.bind(this));
                } else {
                    // Merge missing properties
                    for (const prop in defaultValue) {
                        if ((this.vo as any)[key][prop] == null) {
                            (this.vo as any)[key][prop] = defaultValue[prop];
                        }
                    }
                }
            });
        }

        /**
         * Transform data (convert coordinate objects to cc.Vec2)
         * @param data - Data to transform
         * @returns Transformed data
         */
        private toform<U>(data: U): U {
            if (data && typeof data === "object") {
                const obj = data as any;
                if (obj.x != null && obj.y != null) {
                    return cc.v2(obj.x, obj.y) as any;
                } else {
                    for (const key in obj) {
                        obj[key] = this.toform(obj[key]);
                    }
                }
            }
            return data;
        }

        /**
         * Read data from storage
         * @returns Loaded data or null
         */
        ReadData(): T | null {
            return ReadData<T>(this.name);
        }

        /**
         * Save data to storage (with debouncing)
         */
        SaveData(): void {
            if (this.sTimer) {
                Time.timeDelay.cancelBy(this.sTimer);
            }
            
            const watcher = Time.delay(0.3, () => {
                const dataString = JSON.stringify(this.vo);
                SaveData(this.name, dataString);
                Manager.vo.pushRemoteDataSave();
            });
            
            this.sTimer = watcher?.id;
        }

        /**
         * Set multiple values at once
         * @param values - Object with values to set
         * @param forceSave - Force save even if auto-save is disabled
         */
        SetVal(values: Partial<T>, forceSave: boolean = false): void {
            for (const key in values) {
                (this.vo as any)[key] = values[key];
            }
            
            if (this.isAutoSave || forceSave) {
                this.SaveData();
            }
        }

        /**
         * Clean data from storage
         */
        CleanData(): void {
            CleanData(this.name);
        }

        /**
         * Destroy the manager and clean up listeners
         */
        destroy(): void {
            Notifier.removeListener(NotifyID.Time_NewDay, this.onNewDay, this);
            if (this.sTimer) {
                Time.timeDelay.cancelBy(this.sTimer);
            }
        }
    }
}
