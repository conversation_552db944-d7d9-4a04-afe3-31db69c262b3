/**
 * Mathematical utility functions
 */
export class MathUtils {
    /**
     * Clamp a value between min and max
     * @param value - The value to clamp
     * @param min - The minimum value
     * @param max - The maximum value
     * @returns The clamped value
     */
    static Clamp(value: number, min: number, max: number): number {
        let result = value;
        if (value < min) {
            result = min;
        } else if (value > max) {
            result = max;
        }
        return result;
    }

    /**
     * Linear interpolation between two values
     * @param a - Start value
     * @param b - End value
     * @param t - Interpolation factor (0-1)
     * @returns Interpolated value
     */
    static Lerp(a: number, b: number, t: number): number {
        return a + (b - a) * this.Clamp(t, 0, 1);
    }

    /**
     * Check if a value is approximately equal to another
     * @param a - First value
     * @param b - Second value
     * @param epsilon - Tolerance for comparison
     * @returns True if values are approximately equal
     */
    static Approximately(a: number, b: number, epsilon: number = 0.0001): boolean {
        return Math.abs(a - b) < epsilon;
    }

    /**
     * Convert degrees to radians
     * @param degrees - Angle in degrees
     * @returns Angle in radians
     */
    static DegreesToRadians(degrees: number): number {
        return degrees * Math.PI / 180;
    }

    /**
     * Convert radians to degrees
     * @param radians - Angle in radians
     * @returns Angle in degrees
     */
    static RadiansToDegrees(radians: number): number {
        return radians * 180 / Math.PI;
    }

    /**
     * Get the sign of a number
     * @param value - The number to check
     * @returns 1 for positive, -1 for negative, 0 for zero
     */
    static Sign(value: number): number {
        if (value > 0) return 1;
        if (value < 0) return -1;
        return 0;
    }

    /**
     * Get the absolute value
     * @param value - The number
     * @returns Absolute value
     */
    static Abs(value: number): number {
        return Math.abs(value);
    }

    /**
     * Get the minimum of two values
     * @param a - First value
     * @param b - Second value
     * @returns Minimum value
     */
    static Min(a: number, b: number): number {
        return Math.min(a, b);
    }

    /**
     * Get the maximum of two values
     * @param a - First value
     * @param b - Second value
     * @returns Maximum value
     */
    static Max(a: number, b: number): number {
        return Math.max(a, b);
    }

    /**
     * Round a number to the nearest integer
     * @param value - The number to round
     * @returns Rounded value
     */
    static Round(value: number): number {
        return Math.round(value);
    }

    /**
     * Round down to the nearest integer
     * @param value - The number to floor
     * @returns Floored value
     */
    static Floor(value: number): number {
        return Math.floor(value);
    }

    /**
     * Round up to the nearest integer
     * @param value - The number to ceil
     * @returns Ceiled value
     */
    static Ceil(value: number): number {
        return Math.ceil(value);
    }

    /**
     * Get a random number between 0 and 1
     * @returns Random number
     */
    static Random(): number {
        return Math.random();
    }

    /**
     * Get a random integer between min and max (inclusive)
     * @param min - Minimum value
     * @param max - Maximum value
     * @returns Random integer
     */
    static RandomInt(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * Get a random float between min and max
     * @param min - Minimum value
     * @param max - Maximum value
     * @returns Random float
     */
    static RandomFloat(min: number, max: number): number {
        return Math.random() * (max - min) + min;
    }
}
