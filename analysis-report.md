# JavaScript文件分析详细报告

生成时间: 2025/7/8 18:06:14
分析文件总数: 357

## 📊 总体统计

- **单类文件**: 106个文件
- **多类文件**: 15个文件
- **枚举文件**: 24个文件
- **配置文件**: 198个文件
- **全局函数文件**: 2个文件
- **混合类型文件**: 11个文件
- **空文件**: 1个文件

## 📁 单类文件 (106个)

### activityCfg.js

- **文件路径**: `scripts\activityCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - activityCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### ADController.js

- **文件路径**: `scripts\ADController.js`
- **文件大小**: 4415 字符
- **导入数量**: 11
- **导出数量**: 2
- **类定义**: 1个
  - ADController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - ... 还有6个导入

### adRewardCfg.js

- **文件路径**: `scripts\adRewardCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - adRewardCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BagBuffCfg.js

- **文件路径**: `scripts\BagBuffCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BagBuffCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BagGuideCfg.js

- **文件路径**: `scripts\BagGuideCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BagGuideCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BagModeLvCfg.js

- **文件路径**: `scripts\BagModeLvCfg.js`
- **文件大小**: 466 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BagModeLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BagModeSkillPoolCfg.js

- **文件路径**: `scripts\BagModeSkillPoolCfg.js`
- **文件大小**: 501 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BagModeSkillPoolCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### bagMonsterLvCfg.js

- **文件路径**: `scripts\bagMonsterLvCfg.js`
- **文件大小**: 481 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - bagMonsterLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BagShopItemCfg.js

- **文件路径**: `scripts\BagShopItemCfg.js`
- **文件大小**: 476 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BagShopItemCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BagSkillCfg.js

- **文件路径**: `scripts\BagSkillCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BagSkillCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BottomBarController.js

- **文件路径**: `scripts\BottomBarController.js`
- **文件大小**: 1695 字符
- **导入数量**: 5
- **导出数量**: 2
- **类定义**: 1个
  - BottomBarController (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2UIManager <- "UIManager"
  - $2BottomBarModel <- "BottomBarModel"

### BottomBarView.js

- **文件路径**: `scripts\BottomBarView.js`
- **文件大小**: 4827 字符
- **导入数量**: 7
- **导出数量**: 2
- **类定义**: 1个
  - BottomBarView (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2SoundCfg <- "SoundCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - ... 还有2个导入

### BoxLevelExpCfg.js

- **文件路径**: `scripts\BoxLevelExpCfg.js`
- **文件大小**: 476 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BoxLevelExpCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### Buff.js

- **文件路径**: `scripts\Buff.js`
- **文件大小**: 19212 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 1个
  - t (static_object)
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2Notifier <- "Notifier"
  - ... 还有3个导入

### BuffCfg.js

- **文件路径**: `scripts\BuffCfg.js`
- **文件大小**: 441 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BuffCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BuffController.js

- **文件路径**: `scripts\BuffController.js`
- **文件大小**: 1196 字符
- **导入数量**: 3
- **导出数量**: 2
- **类定义**: 1个
  - BuffController (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Game <- "Game"
  - $2BuffModel <- "BuffModel"

### BuildModeSkiilpoolCfg.js

- **文件路径**: `scripts\BuildModeSkiilpoolCfg.js`
- **文件大小**: 511 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BuildModeSkiilpoolCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### BulletBase.js

- **文件路径**: `scripts\BulletBase.js`
- **文件大小**: 6776 字符
- **导入数量**: 9
- **导出数量**: 1
- **类定义**: 1个
  - o (static_object)
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2Cfg <- "Cfg"
  - $2Manager <- "Manager"
  - $2FCollider <- "FCollider"
  - $2Game <- "Game"
  - ... 还有4个导入

### BulletEffectCfg.js

- **文件路径**: `scripts\BulletEffectCfg.js`
- **文件大小**: 481 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - BulletEffectCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### ByteDance.js

- **文件路径**: `scripts\ByteDance.js`
- **文件大小**: 15872 字符
- **导入数量**: 11
- **导出数量**: 1
- **类定义**: 1个
  - r (static_object)
- **主要导入**:
  - $2BaseSdk <- "BaseSdk"
  - $2AudioAdapter <- "AudioAdapter"
  - $2GameUtil <- "GameUtil"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - ... 还有6个导入

### dmmItemCfg.js

- **文件路径**: `scripts\dmmItemCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - dmmItemCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### dmmRoleCfg.js

- **文件路径**: `scripts\dmmRoleCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - dmmRoleCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### dragonPathCfg.js

- **文件路径**: `scripts\dragonPathCfg.js`
- **文件大小**: 471 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - dragonPathCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### EaseScaleTransition.js

- **文件路径**: `scripts\EaseScaleTransition.js`
- **文件大小**: 2603 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - EaseScaleTransition (class)
- **主要导入**:
  - $1$2MVC <- "MVC"

### EquipLvCfg.js

- **文件路径**: `scripts\EquipLvCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - EquipLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### EquipMergeLvCfg.js

- **文件路径**: `scripts\EquipMergeLvCfg.js`
- **文件大小**: 481 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - EquipMergeLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### ExchangeCodeView.js

- **文件路径**: `scripts\ExchangeCodeView.js`
- **文件大小**: 9316 字符
- **导入数量**: 13
- **导出数量**: 1
- **类定义**: 1个
  - t (static_object)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - ... 还有8个导入

### FightController.js

- **文件路径**: `scripts\FightController.js`
- **文件大小**: 2845 字符
- **导入数量**: 10
- **导出数量**: 2
- **类定义**: 1个
  - FightController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有5个导入

### FightScene.js

- **文件路径**: `scripts\FightScene.js`
- **文件大小**: 2383 字符
- **导入数量**: 4
- **导出数量**: 2
- **类定义**: 1个
  - FightScene (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2ADModel <- "ADModel"

### GameCamera.js

- **文件路径**: `scripts\GameCamera.js`
- **文件大小**: 3723 字符
- **导入数量**: 4
- **导出数量**: 2
- **类定义**: 1个
  - GameCamera (class)
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2Manager <- "Manager"
  - $2Time <- "Time"
  - $2Game <- "Game"

### GuideCfg.js

- **文件路径**: `scripts\GuideCfg.js`
- **文件大小**: 446 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - GuideCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### GuidesController.js

- **文件路径**: `scripts\GuidesController.js`
- **文件大小**: 1975 字符
- **导入数量**: 7
- **导出数量**: 2
- **类定义**: 1个
  - GuidesController (class)
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - $2UIManager <- "UIManager"
  - ... 还有2个导入

### HttpClient.js

- **文件路径**: `scripts\HttpClient.js`
- **文件大小**: 3527 字符
- **导入数量**: 0
- **导出数量**: 2
- **类定义**: 1个
  - HttpClient (static_class)

### Intersection.js

- **文件路径**: `scripts\Intersection.js`
- **文件大小**: 9419 字符
- **导入数量**: 0
- **导出数量**: 3
- **类定义**: 1个
  - Intersection (static_class)

### ItemController.js

- **文件路径**: `scripts\ItemController.js`
- **文件大小**: 3156 字符
- **导入数量**: 12
- **导出数量**: 2
- **类定义**: 1个
  - ItemController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有7个导入

### JUHEAndroid.js

- **文件路径**: `scripts\JUHEAndroid.js`
- **文件大小**: 5657 字符
- **导入数量**: 1
- **导出数量**: 1
- **类定义**: 1个
  - n (static_object)
- **主要导入**:
  - $2BaseSdk <- "BaseSdk"

### languageCfg.js

- **文件路径**: `scripts\languageCfg.js`
- **文件大小**: 539 字符
- **导入数量**: 1
- **导出数量**: 3
- **类定义**: 1个
  - languageCfgReader (class)
- **主要导入**:
  - $2TConfig <- "TConfig"

### LanguageFun.js

- **文件路径**: `scripts\LanguageFun.js`
- **文件大小**: 729 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - LanguageFun (static_class)
- **主要导入**:
  - $2SdkConfig <- "SdkConfig"

### LevelExpCfg.js

- **文件路径**: `scripts\LevelExpCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - LevelExpCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### LoadingController.js

- **文件路径**: `scripts\LoadingController.js`
- **文件大小**: 2298 字符
- **导入数量**: 6
- **导出数量**: 2
- **类定义**: 1个
  - LoadingController (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - $2UIManager <- "UIManager"
  - ... 还有1个导入

### LoadingView.js

- **文件路径**: `scripts\LoadingView.js`
- **文件大小**: 1690 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - LoadingView (class)
- **主要导入**:
  - $2MVC <- "MVC"

### Logger.js

- **文件路径**: `scripts\Logger.js`
- **文件大小**: 3431 字符
- **导入数量**: 1
- **导出数量**: 4
- **类定义**: 1个
  - o (static_object)
- **主要导入**:
  - $2Manager <- "Manager"

### LvInsideCfg.js

- **文件路径**: `scripts\LvInsideCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - LvInsideCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### LvOutsideCfg.js

- **文件路径**: `scripts\LvOutsideCfg.js`
- **文件大小**: 466 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - LvOutsideCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### M20_PartItem.js

- **文件路径**: `scripts\M20_PartItem.js`
- **文件大小**: 21053 字符
- **导入数量**: 16
- **导出数量**: 1
- **类定义**: 1个
  - o (static_object)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - ... 还有11个导入

### M20_Pop_GetBox.js

- **文件路径**: `scripts\M20_Pop_GetBox.js`
- **文件大小**: 6439 字符
- **导入数量**: 12
- **导出数量**: 1
- **类定义**: 1个
  - s (static_object)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - $2Notifier <- "Notifier"
  - ... 还有7个导入

### M33_FightScene.js

- **文件路径**: `scripts\M33_FightScene.js`
- **文件大小**: 2664 字符
- **导入数量**: 7
- **导出数量**: 2
- **类定义**: 1个
  - M33_FightScene (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2UIManager <- "UIManager"
  - $2FightScene <- "FightScene"
  - ... 还有2个导入

### M33_FightUIView.js

- **文件路径**: `scripts\M33_FightUIView.js`
- **文件大小**: 10577 字符
- **导入数量**: 14
- **导出数量**: 2
- **类定义**: 1个
  - M33_FightUIView (class)
- **主要导入**:
  - $2AutoAmTool <- "AutoAmTool"
  - $2Cfg <- "Cfg"
  - $2SoundCfg <- "SoundCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有9个导入

### MapCfg.js

- **文件路径**: `scripts\MapCfg.js`
- **文件大小**: 436 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - MapCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### MathUtils.js

- **文件路径**: `scripts\MathUtils.js`
- **文件大小**: 351 字符
- **导入数量**: 0
- **导出数量**: 2
- **类定义**: 1个
  - MathUtils (static_class)

### MCBoss.js

- **文件路径**: `scripts\MCBoss.js`
- **文件大小**: 3893 字符
- **导入数量**: 6
- **导出数量**: 2
- **类定义**: 1个
  - MCBoss (class)
- **主要导入**:
  - $2StateMachine <- "StateMachine"
  - $2Monster <- "Monster"
  - $2Game <- "Game"
  - $2MCBossState <- "MCBossState"
  - $2MonsterState <- "MonsterState"
  - ... 还有1个导入

### MCDragoMutilation.js

- **文件路径**: `scripts\MCDragoMutilation.js`
- **文件大小**: 5360 字符
- **导入数量**: 6
- **导出数量**: 2
- **类定义**: 1个
  - MCDragoMutilation (class)
- **主要导入**:
  - $2GameatrCfg <- "GameatrCfg"
  - $2Manager <- "Manager"
  - $2Intersection <- "Intersection"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - ... 还有1个导入

### MCDragon.js

- **文件路径**: `scripts\MCDragon.js`
- **文件大小**: 4594 字符
- **导入数量**: 11
- **导出数量**: 2
- **类定义**: 1个
  - MCDragon (class)
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2GameSeting <- "GameSeting"
  - $2Manager <- "Manager"
  - ... 还有6个导入

### MCRole.js

- **文件路径**: `scripts\MCRole.js`
- **文件大小**: 10594 字符
- **导入数量**: 16
- **导出数量**: 1
- **类定义**: 1个
  - a (static_object)
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有11个导入

### MiniGameEquipCfg.js

- **文件路径**: `scripts\MiniGameEquipCfg.js`
- **文件大小**: 486 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - MiniGameEquipCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### MiniGameLvCfg.js

- **文件路径**: `scripts\MiniGameLvCfg.js`
- **文件大小**: 471 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - MiniGameLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### ModeAllOutAttackController.js

- **文件路径**: `scripts\ModeAllOutAttackController.js`
- **文件大小**: 3392 字符
- **导入数量**: 9
- **导出数量**: 2
- **类定义**: 1个
  - ModeAllOutAttackController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - ... 还有4个导入

### ModeBackpackHeroController.js

- **文件路径**: `scripts\ModeBackpackHeroController.js`
- **文件大小**: 9084 字符
- **导入数量**: 14
- **导出数量**: 2
- **类定义**: 1个
  - ModeBackpackHeroController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - ... 还有9个导入

### ModeBulletsReboundController.js

- **文件路径**: `scripts\ModeBulletsReboundController.js`
- **文件大小**: 3474 字符
- **导入数量**: 9
- **导出数量**: 2
- **类定义**: 1个
  - ModeBulletsReboundController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - ... 还有4个导入

### ModeBulletsReboundModel.js

- **文件路径**: `scripts\ModeBulletsReboundModel.js`
- **文件大小**: 3381 字符
- **导入数量**: 6
- **导出数量**: 1
- **类定义**: 1个
  - f (static_object)
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2GameSeting <- "GameSeting"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - ... 还有1个导入

### ModeChainsController.js

- **文件路径**: `scripts\ModeChainsController.js`
- **文件大小**: 3953 字符
- **导入数量**: 10
- **导出数量**: 2
- **类定义**: 1个
  - ModeChainsController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - ... 还有5个导入

### ModeChainsModel.js

- **文件路径**: `scripts\ModeChainsModel.js`
- **文件大小**: 6214 字符
- **导入数量**: 11
- **导出数量**: 1
- **类定义**: 1个
  - v (static_object)
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2MVC <- "MVC"
  - $2GameSeting <- "GameSeting"
  - $2Manager <- "Manager"
  - ... 还有6个导入

### ModeDragonWarController.js

- **文件路径**: `scripts\ModeDragonWarController.js`
- **文件大小**: 2523 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 1个
  - ModeDragonWarController (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2ModeDragonWarModel <- "ModeDragonWarModel"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2UIManager <- "UIManager"
  - ... 还有3个导入

### ModeManGuardsController.js

- **文件路径**: `scripts\ModeManGuardsController.js`
- **文件大小**: 3301 字符
- **导入数量**: 9
- **导出数量**: 2
- **类定义**: 1个
  - ModeManGuardsController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - ... 还有4个导入

### ModePickUpBulletsController.js

- **文件路径**: `scripts\ModePickUpBulletsController.js`
- **文件大小**: 3403 字符
- **导入数量**: 9
- **导出数量**: 2
- **类定义**: 1个
  - ModePickUpBulletsController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - ... 还有4个导入

### ModeThrowingKnifeController.js

- **文件路径**: `scripts\ModeThrowingKnifeController.js`
- **文件大小**: 2725 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 1个
  - ModeThrowingKnifeController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - ... 还有3个导入

### Monster.js

- **文件路径**: `scripts\Monster.js`
- **文件大小**: 11117 字符
- **导入数量**: 12
- **导出数量**: 2
- **类定义**: 1个
  - Monster (class)
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2StateMachine <- "StateMachine"
  - $2GameUtil <- "GameUtil"
  - ... 还有7个导入

### MonsterCfg.js

- **文件路径**: `scripts\MonsterCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - MonsterCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### MonsterLvCfg.js

- **文件路径**: `scripts\MonsterLvCfg.js`
- **文件大小**: 466 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - MonsterLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### Notifier.js

- **文件路径**: `scripts\Notifier.js`
- **文件大小**: 2129 字符
- **导入数量**: 2
- **导出数量**: 7
- **类定义**: 1个
  - Notifier (static_class)
- **主要导入**:
  - $2NotifyListener <- "NotifyListener"
  - $2NotifyCaller <- "NotifyCaller"

### PayController.js

- **文件路径**: `scripts\PayController.js`
- **文件大小**: 3151 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 1个
  - PayController (class)
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - ... 还有3个导入

### PayShopCfg.js

- **文件路径**: `scripts\PayShopCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - PayShopCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### PoolListCfg.js

- **文件路径**: `scripts\PoolListCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - PoolListCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### Pop.js

- **文件路径**: `scripts\Pop.js`
- **文件大小**: 2555 字符
- **导入数量**: 6
- **导出数量**: 2
- **类定义**: 1个
  - Pop (class)
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2UIManager <- "UIManager"
  - $2EaseScaleTransition <- "EaseScaleTransition"
  - $2MVC <- "MVC"
  - ... 还有1个导入

### ProcessRewardsCfg.js

- **文件路径**: `scripts\ProcessRewardsCfg.js`
- **文件大小**: 491 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - ProcessRewardsCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### randomNameCfg.js

- **文件路径**: `scripts\randomNameCfg.js`
- **文件大小**: 471 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - randomNameCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### RBadgeController.js

- **文件路径**: `scripts\RBadgeController.js`
- **文件大小**: 3457 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 1个
  - RBadgeController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有3个导入

### ResUtil.js

- **文件路径**: `scripts\ResUtil.js`
- **文件大小**: 545 字符
- **导入数量**: 2
- **导出数量**: 2
- **类定义**: 1个
  - ResUtil (static_class)
- **主要导入**:
  - $2ResKeeper <- "ResKeeper"
  - $2AssetLoader <- "AssetLoader"

### RoleCfg.js

- **文件路径**: `scripts\RoleCfg.js`
- **文件大小**: 441 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - RoleCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### RoleLvCfg.js

- **文件路径**: `scripts\RoleLvCfg.js`
- **文件大小**: 451 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - RoleLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### RoleUnlockCfg.js

- **文件路径**: `scripts\RoleUnlockCfg.js`
- **文件大小**: 471 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - RoleUnlockCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### SettingController.js

- **文件路径**: `scripts\SettingController.js`
- **文件大小**: 3894 字符
- **导入数量**: 7
- **导出数量**: 2
- **类定义**: 1个
  - SettingController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有2个导入

### SettingView.js

- **文件路径**: `scripts\SettingView.js`
- **文件大小**: 10399 字符
- **导入数量**: 14
- **导出数量**: 2
- **类定义**: 1个
  - SettingView (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Pop <- "Pop"
  - ... 还有9个导入

### ShopController.js

- **文件路径**: `scripts\ShopController.js`
- **文件大小**: 2329 字符
- **导入数量**: 6
- **导出数量**: 2
- **类定义**: 1个
  - ShopController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Time <- "Time"
  - ... 还有1个导入

### signCfg.js

- **文件路径**: `scripts\signCfg.js`
- **文件大小**: 441 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - signCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### SkiilpoolCfg.js

- **文件路径**: `scripts\SkiilpoolCfg.js`
- **文件大小**: 466 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - SkiilpoolCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### SkillCfg.js

- **文件路径**: `scripts\SkillCfg.js`
- **文件大小**: 446 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - SkillCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### SkillController.js

- **文件路径**: `scripts\SkillController.js`
- **文件大小**: 1031 字符
- **导入数量**: 3
- **导出数量**: 2
- **类定义**: 1个
  - SkillController (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Game <- "Game"
  - $2SkillModel <- "SkillModel"

### SkillModule.js

- **文件路径**: `scripts\SkillModule.js`
- **文件大小**: 23180 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 1个
  - BaseSkill (ccclass) [装饰器]
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2GameatrCfg <- "GameatrCfg"
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - ... 还有3个导入

### StorageManager.js

- **文件路径**: `scripts\StorageManager.js`
- **文件大小**: 6858 字符
- **导入数量**: 5
- **导出数量**: 2
- **类定义**: 1个
  - t (static_object)
- **主要导入**:
  - $2Log <- "Log"
  - $2StorageID <- "StorageID"
  - $2lzstring <- "lzstring"
  - $2Manager <- "Manager"
  - $2Time <- "Time"

### TaskCfg.js

- **文件路径**: `scripts\TaskCfg.js`
- **文件大小**: 441 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TaskCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### TaskTypeCfg.js

- **文件路径**: `scripts\TaskTypeCfg.js`
- **文件大小**: 461 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TaskTypeCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### TestController.js

- **文件路径**: `scripts\TestController.js`
- **文件大小**: 2540 字符
- **导入数量**: 6
- **导出数量**: 2
- **类定义**: 1个
  - TestController (class)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2UIManager <- "UIManager"
  - $2Game <- "Game"
  - ... 还有1个导入

### TestView.js

- **文件路径**: `scripts\TestView.js`
- **文件大小**: 17466 字符
- **导入数量**: 19
- **导出数量**: 2
- **类定义**: 1个
  - TestView (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有14个导入

### TideDefendController.js

- **文件路径**: `scripts\TideDefendController.js`
- **文件大小**: 4090 字符
- **导入数量**: 9
- **导出数量**: 2
- **类定义**: 1个
  - TideDefendController (class)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2ListenID <- "ListenID"
  - ... 还有4个导入

### TideDefendModel.js

- **文件路径**: `scripts\TideDefendModel.js`
- **文件大小**: 6359 字符
- **导入数量**: 7
- **导出数量**: 1
- **类定义**: 1个
  - f (static_object)
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2GameSeting <- "GameSeting"
  - $2GameUtil <- "GameUtil"
  - $2RecordVo <- "RecordVo"
  - ... 还有2个导入

### TowerAmethystRewardCfg.js

- **文件路径**: `scripts\TowerAmethystRewardCfg.js`
- **文件大小**: 516 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TowerAmethystRewardCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### TowerCfg.js

- **文件路径**: `scripts\TowerCfg.js`
- **文件大小**: 446 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TowerCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### TowerCoinRewardCfg.js

- **文件路径**: `scripts\TowerCoinRewardCfg.js`
- **文件大小**: 496 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TowerCoinRewardCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### TowerLvCfg.js

- **文件路径**: `scripts\TowerLvCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TowerLvCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### TowerMenuCfg.js

- **文件路径**: `scripts\TowerMenuCfg.js`
- **文件大小**: 466 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TowerMenuCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### TwoDHorizontalLayoutObject.js

- **文件路径**: `scripts\TwoDHorizontalLayoutObject.js`
- **文件大小**: 971 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - TwoDHorizontalLayoutObject (class)
- **主要导入**:
  - $1$2TwoDLayoutObject <- "TwoDLayoutObject"

### UIManager.js

- **文件路径**: `scripts\UIManager.js`
- **文件大小**: 9938 字符
- **导入数量**: 6
- **导出数量**: 2
- **类定义**: 1个
  - UIManager (static_class)
- **主要导入**:
  - $2Log <- "Log"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2Manager <- "Manager"
  - ... 还有1个导入

### UserVo.js

- **文件路径**: `scripts\UserVo.js`
- **文件大小**: 2991 字符
- **导入数量**: 1
- **导出数量**: 3
- **类定义**: 1个
  - e (static_object)
- **主要导入**:
  - $2Manager <- "Manager"

### WeatherCfg.js

- **文件路径**: `scripts\WeatherCfg.js`
- **文件大小**: 456 字符
- **导入数量**: 1
- **导出数量**: 2
- **类定义**: 1个
  - WeatherCfgReader (class)
- **主要导入**:
  - $1$2TConfig <- "TConfig"

### WebDev.js

- **文件路径**: `scripts\WebDev.js`
- **文件大小**: 2548 字符
- **导入数量**: 6
- **导出数量**: 1
- **类定义**: 1个
  - n (static_object)
- **主要导入**:
  - $2BaseSdk <- "BaseSdk"
  - $2SelectAlertAdapter <- "SelectAlertAdapter"
  - $2GameUtil <- "GameUtil"
  - $2Manager <- "Manager"
  - $2StorageID <- "StorageID"
  - ... 还有1个导入

## 📁 多类文件 (15个)

### AssetLoader.js

- **文件路径**: `scripts\AssetLoader.js`
- **文件大小**: 4092 字符
- **导入数量**: 2
- **导出数量**: 4
- **类定义**: 2个
  - LoadResArgs (static_class)
  - e (static_object)
- **主要导入**:
  - $2Log <- "Log"
  - $2ResKeeper <- "ResKeeper"

### BuffList.js

- **文件路径**: `scripts\BuffList.js`
- **文件大小**: 29978 字符
- **导入数量**: 11
- **导出数量**: 36
- **类定义**: 37个
  - Buff_Default (ccclass) [装饰器]
  - Buff_Excute (ccclass) [装饰器]
  - Buff_OnTime (ccclass) [装饰器]
  - Buff_Effect (ccclass) [装饰器]
  - Buff_OnSpawnHurt (ccclass) [装饰器]
  - Buff_OnVampirism (ccclass) [装饰器]
  - Buff_CurrencyReward (ccclass) [装饰器]
  - Buff_OnBehit (ccclass) [装饰器]
  - Buff_OnKill (ccclass) [装饰器]
  - Buff_HPLink (ccclass) [装饰器]
  - Buff_ContinuousRecovery (ccclass) [装饰器]
  - Buff_HPLinkOnce (ccclass) [装饰器]
  - Buff_EntityDead (ccclass) [装饰器]
  - Buff_VicinityHurt (ccclass) [装饰器]
  - Buff_Halo (ccclass) [装饰器]
  - Buff_Hurt (ccclass) [装饰器]
  - Buff_OnUseSkillHurt (ccclass) [装饰器]
  - Buff_SubSkill (ccclass) [装饰器]
  - Buff_AtkFocus (ccclass) [装饰器]
  - Buff_AdrenalTechnology (ccclass) [装饰器]
  - Buff_OnSkillUseUnload (ccclass) [装饰器]
  - Buff_ReboundDam (ccclass) [装饰器]
  - Buff_OnKillLayout (ccclass) [装饰器]
  - Buff_HitBack (ccclass) [装饰器]
  - Buff_OnLifeVal (ccclass) [装饰器]
  - Buff_OnSpawnHurtAddArmor (ccclass) [装饰器]
  - Buff_OnBehitAddArmor (ccclass) [装饰器]
  - Buff_RestoreArmor (ccclass) [装饰器]
  - Buff_OnSkill (ccclass) [装饰器]
  - Buff_Vampire (ccclass) [装饰器]
  - Buff_AssociationProp (ccclass) [装饰器]
  - Buff_ResistDamage (ccclass) [装饰器]
  - Buff_SetSlash (ccclass) [装饰器]
  - Buff_OnRoundState (ccclass) [装饰器]
  - Buff_ReplaceRole (ccclass) [装饰器]
  - o (static_object)
  - o (static_object)
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2Notifier <- "Notifier"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - ... 还有6个导入

### Dragon.js

- **文件路径**: `scripts\Dragon.js`
- **文件大小**: 10702 字符
- **导入数量**: 10
- **导出数量**: 2
- **类定义**: 2个
  - Dragon (class)
  - a (static_object)
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2Manager <- "Manager"
  - $2Intersection <- "Intersection"
  - $2GameUtil <- "GameUtil"
  - ... 还有5个导入

### EventController.js

- **文件路径**: `scripts\EventController.js`
- **文件大小**: 5056 字符
- **导入数量**: 7
- **导出数量**: 2
- **类定义**: 2个
  - EventController (class)
  - e (static_object)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2SdkConfig <- "SdkConfig"
  - ... 还有2个导入

### FightUIView.js

- **文件路径**: `scripts\FightUIView.js`
- **文件大小**: 2826 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 2个
  - FightUIView (class)
  - m (static_object)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - $2Notifier <- "Notifier"
  - ... 还有3个导入

### function.js

- **文件路径**: `scripts\function.js`
- **文件大小**: 2091 字符
- **导入数量**: 3
- **导出数量**: 9
- **类定义**: 2个
  - t (static_object)
  - o (static_object)
- **主要导入**:
  - $2Api <- "Api"
  - $2md51 <- "md51"
  - $2config <- "config"

### GameUtil.js

- **文件路径**: `scripts\GameUtil.js`
- **文件大小**: 24150 字符
- **导入数量**: 4
- **导出数量**: 10
- **类定义**: 6个
  - GameUtil (class)
  - r (static_object)
  - o (static_object)
  - n (static_object)
  - o (static_object)
  - o (static_object)
- **主要导入**:
  - $2LanguageFun <- "LanguageFun"
  - $2Time <- "Time"
  - $2Md5 <- "Md5"
  - $2lzstring <- "lzstring"

### lzstring.js

- **文件路径**: `scripts\lzstring.js`
- **文件大小**: 11540 字符
- **导入数量**: 0
- **导出数量**: 0
- **类定义**: 3个
  - r (static_object)
  - s (static_object)
  - m (static_object)

### M20Prop_Equip.js

- **文件路径**: `scripts\M20Prop_Equip.js`
- **文件大小**: 28527 字符
- **导入数量**: 18
- **导出数量**: 1
- **类定义**: 8个
  - o (static_object)
  - n (static_object)
  - r (static_object)
  - r (static_object)
  - a (static_object)
  - a (static_object)
  - a (static_object)
  - a (static_object)
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2GameatrCfg <- "GameatrCfg"
  - ... 还有13个导入

### ModeBackpackHeroModel.js

- **文件路径**: `scripts\ModeBackpackHeroModel.js`
- **文件大小**: 34501 字符
- **导入数量**: 16
- **导出数量**: 3
- **类定义**: 3个
  - h (static_object)
  - a (static_object)
  - s (static_object)
- **主要导入**:
  - $2MVC <- "MVC"
  - $2KnapsackVo <- "KnapsackVo"
  - $2Game <- "Game"
  - $2Cfg <- "Cfg"
  - $2GameUtil <- "GameUtil"
  - ... 还有11个导入

### NetAdapter.js

- **文件路径**: `scripts\NetAdapter.js`
- **文件大小**: 3528 字符
- **导入数量**: 2
- **导出数量**: 2
- **类定义**: 2个
  - o (static_object)
  - e (static_object)
- **主要导入**:
  - $2NetManager <- "NetManager"
  - $2Manager <- "Manager"

### ReflexBullet.js

- **文件路径**: `scripts\ReflexBullet.js`
- **文件大小**: 2926 字符
- **导入数量**: 2
- **导出数量**: 1
- **类定义**: 3个
  - o (static_object)
  - t (static_object)
  - o (static_object)
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2Bullet <- "Bullet"

### Report.js

- **文件路径**: `scripts\Report.js`
- **文件大小**: 8322 字符
- **导入数量**: 6
- **导出数量**: 1
- **类定义**: 11个
  - e (static_object)
  - o (static_object)
  - a (static_object)
  - o (static_object)
  - t (static_object)
  - t (static_object)
  - t (static_object)
  - t (static_object)
  - t (static_object)
  - o (static_object)
  - o (static_object)
- **主要导入**:
  - $2Api <- "Api"
  - $2function <- "function"
  - $2TimeManage <- "TimeManage"
  - $2config <- "config"
  - $2Notifier <- "Notifier"
  - ... 还有1个导入

### RoleSkillList.js

- **文件路径**: `scripts\RoleSkillList.js`
- **文件大小**: 43753 字符
- **导入数量**: 13
- **导出数量**: 36
- **类定义**: 35个
  - Skill_Default (ccclass) [装饰器]
  - Skill_AtkDefault (ccclass) [装饰器]
  - Skill_NormalChop (ccclass) [装饰器]
  - Skill_MagicBall (ccclass) [装饰器]
  - Skill_Multishot (ccclass) [装饰器]
  - Skill_FireOffset (ccclass) [装饰器]
  - Skill_ParallelFire (ccclass) [装饰器]
  - Skill_Sniper (ccclass) [装饰器]
  - Skill_MultiBullet (ccclass) [装饰器]
  - Skill_StruckLightning (ccclass) [装饰器]
  - Skill_Meteorite (ccclass) [装饰器]
  - Skill_RingFireballs (ccclass) [装饰器]
  - Skill_RangeBomb (ccclass) [装饰器]
  - Skill_LaserRadiation (ccclass) [装饰器]
  - Skill_LaserAnacampsis (ccclass) [装饰器]
  - Skill_Ligature (ccclass) [装饰器]
  - Skill_LaserRadiationGuard (ccclass) [装饰器]
  - Skill_Arrows (ccclass) [装饰器]
  - Skill_Venom (ccclass) [装饰器]
  - Skill_Icicle (ccclass) [装饰器]
  - Skill_BounceThrowingKnife (ccclass) [装饰器]
  - Skill_Whirlwind (ccclass) [装饰器]
  - Skill_Tornado (ccclass) [装饰器]
  - Skill_Continuous (ccclass) [装饰器]
  - Skill_GoldenCudgel (ccclass) [装饰器]
  - Skill_SwordSword (ccclass) [装饰器]
  - Skill_EffectSkill (ccclass) [装饰器]
  - Skill_ColdAir (ccclass) [装饰器]
  - Skill_PosExcute (ccclass) [装饰器]
  - Skill_StampSweep (ccclass) [装饰器]
  - Skill_PowerStorage (ccclass) [装饰器]
  - Skill_Flower (ccclass) [装饰器]
  - Skill_Magazine (ccclass) [装饰器]
  - Skill_FollowPath (ccclass) [装饰器]
  - Skill_MCDragonFollowPath (ccclass) [装饰器]
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2AutoFollow <- "AutoFollow"
  - $2GameatrCfg <- "GameatrCfg"
  - $2SoundCfg <- "SoundCfg"
  - ... 还有8个导入

### WonderSdk.js

- **文件路径**: `scripts\WonderSdk.js`
- **文件大小**: 19671 字符
- **导入数量**: 10
- **导出数量**: 2
- **类定义**: 2个
  - WonderSdk (static_class)
  - n (static_object)
- **主要导入**:
  - $2CallID <- "CallID"
  - $2StorageID <- "StorageID"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - $2ModeBackpackHeroModel <- "ModeBackpackHeroModel"
  - ... 还有5个导入

## 📁 枚举文件 (24个)

### AudioManager.js

- **文件路径**: `scripts\AudioManager.js`
- **文件大小**: 8843 字符
- **导入数量**: 5
- **导出数量**: 4
- **枚举定义**: 2个
  - 枚举1: UI, POSINDEX1, POSINDEX2, POSINDEX3, POSINDEX4, POSINDEX5, POSINDEX6, POSINDEX7, POSINDEX8, POSINDEX...
  - 枚举2: Component, Scripts
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2SettingModel <- "SettingModel"
  - $2Manager <- "Manager"

### BaseEntity.js

- **文件路径**: `scripts\BaseEntity.js`
- **文件大小**: 8043 字符
- **导入数量**: 2
- **导出数量**: 4
- **枚举定义**: 1个
  - 枚举1: Not, One, Two, Three
- **主要导入**:
  - $2FCollider <- "FCollider"
  - $2Game <- "Game"

### BaseSdk.js

- **文件路径**: `scripts\BaseSdk.js`
- **文件大小**: 3841 字符
- **导入数量**: 0
- **导出数量**: 4
- **枚举定义**: 2个
  - 枚举1: COMPLETE, NOT_SUPPORT, NOT_READY, UNKNOW_AdId, NOT_COMPLITE, AD_ERROR, SHOW_SUCCESS
  - 枚举2: SHARE_CHALLENGE, SHARE_GROUP, SHARE_NORMAL, SHARE_REWARD, SHARE_VICTORY, SHARE_SORCE, SHARE_RANK, SH...

### CallID.js

- **文件路径**: `scripts\CallID.js`
- **文件大小**: 2512 字符
- **导入数量**: 0
- **导出数量**: 3
- **枚举定义**: 1个
  - 枚举1: _Start, Setting_IsEnableAudio, Setting_IsEnableShake, Setting_GetRealDesignSize, Login_GetServerInfo...

### FCollider.js

- **文件路径**: `scripts\FCollider.js`
- **文件大小**: 4086 字符
- **导入数量**: 2
- **导出数量**: 4
- **枚举定义**: 2个
  - 枚举1: Circle, Box, Polygon
  - 枚举2: IsTest, NoTest
- **主要导入**:
  - $2BaseEntity <- "BaseEntity"
  - $2FColliderManager <- "FColliderManager"

### GameSeting.js

- **文件路径**: `scripts\GameSeting.js`
- **文件大小**: 8473 字符
- **导入数量**: 0
- **导出数量**: 3
- **枚举定义**: 1个
  - 枚举1: Not, Money, Fragment, Goods, Skin, Equip, Cell, Skill, MergeEquip, OpenBuffSelect, BuffDropSelect, R...

### GridView.js

- **文件路径**: `scripts\GridView.js`
- **文件大小**: 13019 字符
- **导入数量**: 8
- **导出数量**: 3
- **枚举定义**: 1个
  - 枚举1: GRID_HORIZONTAL, GRID_VERTICAL
- **主要导入**:
  - $2PoolArray <- "PoolArray"
  - $2GridViewCell <- "GridViewCell"
  - $2TwoDLayoutObject <- "TwoDLayoutObject"
  - $2TwoDHorizontalLayoutObject <- "TwoDHorizontalLayoutObject"
  - $2MathSection <- "MathSection"
  - ... 还有3个导入

### ListenID.js

- **文件路径**: `scripts\ListenID.js`
- **文件大小**: 14238 字符
- **导入数量**: 0
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: _Start, Game_UpdatePower, Game_SecondDay, Game_StartLoadMap, Game_EndLoadMap, Game_Load, Game_Replay...

### MBackpackHero.js

- **文件路径**: `scripts\MBackpackHero.js`
- **文件大小**: 30641 字符
- **导入数量**: 23
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: NONE, BATTLE, ONTICK, SELECTEQUIP, END, MergeEquipment, Block, Gem, Default, Challenge, ChallengeCoi...
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2GameSettingCfg <- "GameSettingCfg"
  - ... 还有18个导入

### MBRebound.js

- **文件路径**: `scripts\MBRebound.js`
- **文件大小**: 13497 字符
- **导入数量**: 19
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: NONE, MyRound, BulletAnim, EnemyRound, Determine, NormalBuff, HighBuff
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - ... 还有14个导入

### MChains.js

- **文件路径**: `scripts\MChains.js`
- **文件大小**: 21136 字符
- **导入数量**: 24
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: NONE, BATTLE, TRANSITIONAM, BOSSCOMEON, END, Forward, D360, Move, ForwardMoveExtend, NormalBuff, Hig...
- **主要导入**:
  - $2MovingBGSprite <- "MovingBGSprite"
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - ... 还有19个导入

### MMGuards.js

- **文件路径**: `scripts\MMGuards.js`
- **文件大小**: 10193 字符
- **导入数量**: 14
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: NONE, Playing, BulletAnim, Determine
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - ... 还有9个导入

### MTideDefendRebound.js

- **文件路径**: `scripts\MTideDefendRebound.js`
- **文件大小**: 19541 字符
- **导入数量**: 17
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: NONE, BATTLE, ONTICK, SELECTEQUIP, END, DRAGON1, DRAGON2, DRAGON3, DRAGON4, Forward, D360, Move, Nor...
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有12个导入

### MTKnife.js

- **文件路径**: `scripts\MTKnife.js`
- **文件大小**: 11061 字符
- **导入数量**: 15
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: NONE, MyRound, BulletAnim, EnemyRound
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - ... 还有10个导入

### MVC.js

- **文件路径**: `scripts\MVC.js`
- **文件大小**: 16556 字符
- **导入数量**: 8
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: Unload, Loading, Loaded, Scene, Main, Panel, Popup, SubPopup, Tips, Guide, Loading, Max, Scene, Pane...
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2Time <- "Time"
  - $2ResKeeper <- "ResKeeper"
  - ... 还有3个导入

### NodePool.js

- **文件路径**: `scripts\NodePool.js`
- **文件大小**: 3758 字符
- **导入数量**: 3
- **导出数量**: 4
- **枚举定义**: 1个
  - 枚举1: WaitLoad, StartLoad, Loaded, TimeOut, Die
- **主要导入**:
  - $2Pool <- "Pool"
  - $2Manager <- "Manager"
  - $2Time <- "Time"

### NotifyID.js

- **文件路径**: `scripts\NotifyID.js`
- **文件大小**: 445 字符
- **导入数量**: 0
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: Game_Update, Time_Scale, Game_Pause, Func_Open, Game_LoadingView, Game_BanClick, Time_NewDay

### PropertyVo.js

- **文件路径**: `scripts\PropertyVo.js`
- **文件大小**: 7623 字符
- **导入数量**: 6
- **导出数量**: 3
- **枚举定义**: 1个
  - 枚举1: Default, Block, Miss, Critical, Slash
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2GameatrCfg <- "GameatrCfg"
  - $2ObjectPool <- "ObjectPool"
  - $2Manager <- "Manager"
  - ... 还有1个导入

### RBadgeModel.js

- **文件路径**: `scripts\RBadgeModel.js`
- **文件大小**: 6150 字符
- **导入数量**: 5
- **导出数量**: 3
- **枚举定义**: 1个
  - 枚举1: Role, Fight, Equip, Activity, Task, Shop_FreeDiamond, Shop_FreeCoin, Fight_NavReward, Fight_DeskRewa...
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Time <- "Time"
  - $2GameUtil <- "GameUtil"

### SdkConfig.js

- **文件路径**: `scripts\SdkConfig.js`
- **文件大小**: 18589 字符
- **导入数量**: 0
- **导出数量**: 22
- **枚举定义**: 1个
  - 枚举1: zh, en, tw

### StateMachine.js

- **文件路径**: `scripts\StateMachine.js`
- **文件大小**: 8757 字符
- **导入数量**: 1
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: IDLE, MOVE, ATTACK, GLOBAL, BEHIT, DEAD, SPRINT, ESCAPE, SKILL, WANDER, FOLLOW, THINK, HIDE, APPEAR
- **主要导入**:
  - $2GameUtil <- "GameUtil"

### StorageID.js

- **文件路径**: `scripts\StorageID.js`
- **文件大小**: 654 字符
- **导入数量**: 0
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: GameTag, Setting_Data, Model_Name, UserData, TempUser, LogUserInfo, LogServerDomain, LastServerId, B...

### TaskModel.js

- **文件路径**: `scripts\TaskModel.js`
- **文件大小**: 10921 字符
- **导入数量**: 7
- **导出数量**: 3
- **枚举定义**: 1个
  - 枚举1: SaveProgressId, TaskProgressData, SaveGetTaskId, GameTaskAchieve, GameTaskDay, GameTaskWeek
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Time <- "Time"
  - ... 还有2个导入

### TrackManger.js

- **文件路径**: `scripts\TrackManger.js`
- **文件大小**: 2591 字符
- **导入数量**: 4
- **导出数量**: 2
- **枚举定义**: 1个
  - 枚举1: RewardBuild, RewardRole, RewardPet, Prop, PropMagnet, PropHp, PropBox
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2TrackItem <- "TrackItem"

## 📁 配置文件 (198个)

### ADModel.js

- **文件路径**: `scripts\ADModel.js`
- **文件大小**: 781 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2GameSeting <- "GameSeting"

### Api.js

- **文件路径**: `scripts\Api.js`
- **文件大小**: 2000 字符
- **导入数量**: 4
- **导出数量**: 5
- **主要导入**:
  - $2Request <- "Request"
  - $2config <- "config"
  - $2md51 <- "md51"
  - $2SdkConfig <- "SdkConfig"

### ArcBullet.js

- **文件路径**: `scripts\ArcBullet.js`
- **文件大小**: 1388 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2BulletBase <- "BulletBase"

### AudioAdapter.js

- **文件路径**: `scripts\AudioAdapter.js`
- **文件大小**: 663 字符
- **导入数量**: 0
- **导出数量**: 2

### AutoAmTool.js

- **文件路径**: `scripts\AutoAmTool.js`
- **文件大小**: 5295 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2GameSeting <- "GameSeting"

### AutoAnimationClip.js

- **文件路径**: `scripts\AutoAnimationClip.js`
- **文件大小**: 3251 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Game <- "Game"

### AutoFollow.js

- **文件路径**: `scripts\AutoFollow.js`
- **文件大小**: 1429 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Notifier <- "Notifier"

### AutoScaleComponent.js

- **文件路径**: `scripts\AutoScaleComponent.js`
- **文件大小**: 956 字符
- **导入数量**: 1
- **导出数量**: 2
- **主要导入**:
  - $2GridView <- "GridView"

### BackHeroProp.js

- **文件路径**: `scripts\BackHeroProp.js`
- **文件大小**: 6961 字符
- **导入数量**: 11
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - ... 还有6个导入

### BackpackHeroHome.js

- **文件路径**: `scripts\BackpackHeroHome.js`
- **文件大小**: 13399 字符
- **导入数量**: 15
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2Notifier <- "Notifier"
  - ... 还有10个导入

### BoomerangBullet.js

- **文件路径**: `scripts\BoomerangBullet.js`
- **文件大小**: 1670 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2BulletBase <- "BulletBase"

### BottomBarModel.js

- **文件路径**: `scripts\BottomBarModel.js`
- **文件大小**: 908 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Game <- "Game"

### BounceBullet.js

- **文件路径**: `scripts\BounceBullet.js`
- **文件大小**: 1909 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Game <- "Game"
  - $2Bullet <- "Bullet"

### BronMonsterManger.js

- **文件路径**: `scripts\BronMonsterManger.js`
- **文件大小**: 8109 字符
- **导入数量**: 9
- **导出数量**: 2
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2GameUtil <- "GameUtil"
  - $2NodePool <- "NodePool"
  - ... 还有4个导入

### BuffCardItem.js

- **文件路径**: `scripts\BuffCardItem.js`
- **文件大小**: 5124 字符
- **导入数量**: 6
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - ... 还有1个导入

### BuffModel.js

- **文件路径**: `scripts\BuffModel.js`
- **文件大小**: 3967 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2RewardEvent <- "RewardEvent"

### Bullet_Arrow.js

- **文件路径**: `scripts\Bullet_Arrow.js`
- **文件大小**: 1574 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2BulletBase <- "BulletBase"

### Bullet_FollowTarget.js

- **文件路径**: `scripts\Bullet_FollowTarget.js`
- **文件大小**: 2454 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2BulletBase <- "BulletBase"

### Bullet_HitReflex.js

- **文件路径**: `scripts\Bullet_HitReflex.js`
- **文件大小**: 1135 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2Bullet <- "Bullet"

### Bullet_Laser.js

- **文件路径**: `scripts\Bullet_Laser.js`
- **文件大小**: 3208 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2BaseEntity <- "BaseEntity"
  - $2BulletBase <- "BulletBase"

### Bullet_Ligature.js

- **文件路径**: `scripts\Bullet_Ligature.js`
- **文件大小**: 2370 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2FBoxCollider <- "FBoxCollider"
  - $2GameUtil <- "GameUtil"
  - $2BulletBase <- "BulletBase"

### Bullet_LigaturePonit.js

- **文件路径**: `scripts\Bullet_LigaturePonit.js`
- **文件大小**: 1688 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2BulletBase <- "BulletBase"

### Bullet_Path.js

- **文件路径**: `scripts\Bullet_Path.js`
- **文件大小**: 1385 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2BulletBase <- "BulletBase"

### Bullet_RandomMove.js

- **文件路径**: `scripts\Bullet_RandomMove.js`
- **文件大小**: 1617 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2Bullet <- "Bullet"

### Bullet_RigidBody.js

- **文件路径**: `scripts\Bullet_RigidBody.js`
- **文件大小**: 2242 字符
- **导入数量**: 8
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2SoundCfg <- "SoundCfg"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - ... 还有3个导入

### Bullet.js

- **文件路径**: `scripts\Bullet.js`
- **文件大小**: 944 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2BulletBase <- "BulletBase"

### BulletVo.js

- **文件路径**: `scripts\BulletVo.js`
- **文件大小**: 3965 字符
- **导入数量**: 3
- **导出数量**: 2
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2BaseEntity <- "BaseEntity"
  - $2PropertyVo <- "PropertyVo"

### BulletVoPool.js

- **文件路径**: `scripts\BulletVoPool.js`
- **文件大小**: 420 字符
- **导入数量**: 1
- **导出数量**: 2
- **主要导入**:
  - $1$2Pool <- "Pool"

### Cfg.js

- **文件路径**: `scripts\Cfg.js`
- **文件大小**: 22368 字符
- **导入数量**: 52
- **导出数量**: 2
- **主要导入**:
  - $2activityCfg <- "activityCfg"
  - $2adRewardCfg <- "adRewardCfg"
  - $2BagShopItemCfg <- "BagShopItemCfg"
  - $2LvOutsideCfg <- "LvOutsideCfg"
  - $2LvInsideCfg <- "LvInsideCfg"
  - ... 还有47个导入

### CircleBullet.js

- **文件路径**: `scripts\CircleBullet.js`
- **文件大小**: 1450 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2BulletBase <- "BulletBase"

### Commonguide.js

- **文件路径**: `scripts\Commonguide.js`
- **文件大小**: 7893 字符
- **导入数量**: 8
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有3个导入

### CompManager.js

- **文件路径**: `scripts\CompManager.js`
- **文件大小**: 1374 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2Game <- "Game"

### config.js

- **文件路径**: `scripts\config.js`
- **文件大小**: 425 字符
- **导入数量**: 0
- **导出数量**: 7

### ContinuousBullet.js

- **文件路径**: `scripts\ContinuousBullet.js`
- **文件大小**: 978 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2GameSkeleton <- "GameSkeleton"
  - $2Bullet <- "Bullet"

### CurrencyTips.js

- **文件路径**: `scripts\CurrencyTips.js`
- **文件大小**: 876 字符
- **导入数量**: 0
- **导出数量**: 1

### DialogBox.js

- **文件路径**: `scripts\DialogBox.js`
- **文件大小**: 1778 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2LanguageFun <- "LanguageFun"
  - $2Game <- "Game"
  - $2NodePool <- "NodePool"

### DragonBody.js

- **文件路径**: `scripts\DragonBody.js`
- **文件大小**: 14341 字符
- **导入数量**: 16
- **导出数量**: 1
- **主要导入**:
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2SoundCfg <- "SoundCfg"
  - $2MVC <- "MVC"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有11个导入

### Effect_Behead.js

- **文件路径**: `scripts\Effect_Behead.js`
- **文件大小**: 951 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Game <- "Game"
  - $2GameEffect <- "GameEffect"

### Effect_Behit.js

- **文件路径**: `scripts\Effect_Behit.js`
- **文件大小**: 1025 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Game <- "Game"
  - $2GameEffect <- "GameEffect"

### EffectSkeleton.js

- **文件路径**: `scripts\EffectSkeleton.js`
- **文件大小**: 2145 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2BulletVoPool <- "BulletVoPool"
  - $2Game <- "Game"
  - $2GameEffect <- "GameEffect"

### EnergyStamp.js

- **文件路径**: `scripts\EnergyStamp.js`
- **文件大小**: 2495 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - $2ModeBackpackHeroModel <- "ModeBackpackHeroModel"

### EntityDieEffect.js

- **文件路径**: `scripts\EntityDieEffect.js`
- **文件大小**: 1179 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2GameEffect <- "GameEffect"

### EventModel.js

- **文件路径**: `scripts\EventModel.js`
- **文件大小**: 704 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $1$2MVC <- "MVC"

### ExSprite.js

- **文件路径**: `scripts\ExSprite.js`
- **文件大小**: 592 字符
- **导入数量**: 0
- **导出数量**: 1

### FBoxCollider.js

- **文件路径**: `scripts\FBoxCollider.js`
- **文件大小**: 1441 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2FCollider <- "FCollider"

### FCircleCollider.js

- **文件路径**: `scripts\FCircleCollider.js`
- **文件大小**: 1331 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2FCollider <- "FCollider"

### FColliderManager.js

- **文件路径**: `scripts\FColliderManager.js`
- **文件大小**: 16417 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2FCollider <- "FCollider"
  - $2Intersection <- "Intersection"
  - $2QuadTree <- "QuadTree"

### FightModel.js

- **文件路径**: `scripts\FightModel.js`
- **文件大小**: 794 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Game <- "Game"

### FPolygonCollider.js

- **文件路径**: `scripts\FPolygonCollider.js`
- **文件大小**: 1614 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2FCollider <- "FCollider"
  - $2Intersection <- "Intersection"

### GameAnimi.js

- **文件路径**: `scripts\GameAnimi.js`
- **文件大小**: 1143 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2Game <- "Game"

### GameEffect.js

- **文件路径**: `scripts\GameEffect.js`
- **文件大小**: 1042 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2BaseEntity <- "BaseEntity"

### GameSkeleton.js

- **文件路径**: `scripts\GameSkeleton.js`
- **文件大小**: 3516 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Game <- "Game"

### GiftPackView.js

- **文件路径**: `scripts\GiftPackView.js`
- **文件大小**: 6205 字符
- **导入数量**: 10
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2ShareButton <- "ShareButton"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - ... 还有5个导入

### Goods.js

- **文件路径**: `scripts\Goods.js`
- **文件大小**: 5349 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2DropConfigCfg <- "DropConfigCfg"
  - ... 还有7个导入

### GoodsUIItem.js

- **文件路径**: `scripts\GoodsUIItem.js`
- **文件大小**: 4178 字符
- **导入数量**: 9
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - ... 还有4个导入

### GridViewCell.js

- **文件路径**: `scripts\GridViewCell.js`
- **文件大小**: 983 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"

### GridViewFreshWork.js

- **文件路径**: `scripts\GridViewFreshWork.js`
- **文件大小**: 1532 字符
- **导入数量**: 1
- **导出数量**: 3
- **主要导入**:
  - $2Time <- "Time"

### GTAssembler2D.js

- **文件路径**: `scripts\GTAssembler2D.js`
- **文件大小**: 4918 字符
- **导入数量**: 0
- **导出数量**: 1

### GTSimpleSpriteAssembler2D.js

- **文件路径**: `scripts\GTSimpleSpriteAssembler2D.js`
- **文件大小**: 1716 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $1$2GTAssembler2D <- "GTAssembler2D"

### GuidesModel.js

- **文件路径**: `scripts\GuidesModel.js`
- **文件大小**: 1625 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $1$2MVC <- "MVC"

### Hide.js

- **文件路径**: `scripts\Hide.js`
- **文件大小**: 230 字符
- **导入数量**: 0
- **导出数量**: 1

### index.js

- **文件路径**: `scripts\index.js`
- **文件大小**: 5624 字符
- **导入数量**: 11
- **导出数量**: 3
- **主要导入**:
  - $2Report <- "Report"
  - $2Show <- "Show"
  - $2Hide <- "Hide"
  - $2Login <- "Login"
  - $2Api <- "Api"
  - ... 还有6个导入

### ItemModel.js

- **文件路径**: `scripts\ItemModel.js`
- **文件大小**: 4655 字符
- **导入数量**: 8
- **导出数量**: 1
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Manager <- "Manager"
  - ... 还有3个导入

### KawaseAnim.js

- **文件路径**: `scripts\KawaseAnim.js`
- **文件大小**: 850 字符
- **导入数量**: 0
- **导出数量**: 1

### KnapsackVo.js

- **文件路径**: `scripts\KnapsackVo.js`
- **文件大小**: 5043 字符
- **导入数量**: 6
- **导出数量**: 2
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - ... 还有1个导入

### LaserRadiationBullet.js

- **文件路径**: `scripts\LaserRadiationBullet.js`
- **文件大小**: 2523 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2GameSkeleton <- "GameSkeleton"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2BulletBase <- "BulletBase"

### LatticeMap.js

- **文件路径**: `scripts\LatticeMap.js`
- **文件大小**: 5686 字符
- **导入数量**: 3
- **导出数量**: 2
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2Game <- "Game"
  - $2BaseEntity <- "BaseEntity"

### Launcher.js

- **文件路径**: `scripts\Launcher.js`
- **文件大小**: 11321 字符
- **导入数量**: 16
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - ... 还有11个导入

### LayoutObject.js

- **文件路径**: `scripts\LayoutObject.js`
- **文件大小**: 455 字符
- **导入数量**: 0
- **导出数量**: 2

### LevelMgr.js

- **文件路径**: `scripts\LevelMgr.js`
- **文件大小**: 3783 字符
- **导入数量**: 5
- **导出数量**: 2
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Manager <- "Manager"
  - $2ItemModel <- "ItemModel"
  - $2ModeBackpackHeroModel <- "ModeBackpackHeroModel"
  - $2RecordVo <- "RecordVo"

### LifeBar.js

- **文件路径**: `scripts\LifeBar.js`
- **文件大小**: 2654 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2NodePool <- "NodePool"
  - $2BaseEntity <- "BaseEntity"
  - $2Notifier <- "Notifier"

### LifeLabel.js

- **文件路径**: `scripts\LifeLabel.js`
- **文件大小**: 1925 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2NodePool <- "NodePool"
  - $2Notifier <- "Notifier"

### LigatureBullet.js

- **文件路径**: `scripts\LigatureBullet.js`
- **文件大小**: 772 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2BulletBase <- "BulletBase"

### LoaderAdapter.js

- **文件路径**: `scripts\LoaderAdapter.js`
- **文件大小**: 8694 字符
- **导入数量**: 3
- **导出数量**: 2
- **主要导入**:
  - $2GameSkeleton <- "GameSkeleton"
  - $2AssetLoader <- "AssetLoader"
  - $2ResUtil <- "ResUtil"

### LoadingModel.js

- **文件路径**: `scripts\LoadingModel.js`
- **文件大小**: 689 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $1$2MVC <- "MVC"

### LocalStorage.js

- **文件路径**: `scripts\LocalStorage.js`
- **文件大小**: 824 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2StorageSync <- "StorageSync"

### Log.js

- **文件路径**: `scripts\Log.js`
- **文件大小**: 1770 字符
- **导入数量**: 0
- **导出数量**: 2

### Login.js

- **文件路径**: `scripts\Login.js`
- **文件大小**: 230 字符
- **导入数量**: 0
- **导出数量**: 1

### M20_Pop_EquipInfo.js

- **文件路径**: `scripts\M20_Pop_EquipInfo.js`
- **文件大小**: 9515 字符
- **导入数量**: 14
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - ... 还有9个导入

### M20_Pop_GameRewardView.js

- **文件路径**: `scripts\M20_Pop_GameRewardView.js`
- **文件大小**: 2895 字符
- **导入数量**: 9
- **导出数量**: 1
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - $2Manager <- "Manager"
  - ... 还有4个导入

### M20_Pop_GetEnergy.js

- **文件路径**: `scripts\M20_Pop_GetEnergy.js`
- **文件大小**: 5019 字符
- **导入数量**: 11
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2VideoButton <- "VideoButton"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - ... 还有6个导入

### M20_Pop_Insufficient_Props_Tips.js

- **文件路径**: `scripts\M20_Pop_Insufficient_Props_Tips.js`
- **文件大小**: 5422 字符
- **导入数量**: 13
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - ... 还有8个导入

### M20_Pop_NewEquipUnlock.js

- **文件路径**: `scripts\M20_Pop_NewEquipUnlock.js`
- **文件大小**: 2948 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - ... 还有7个导入

### M20_Pop_ShopBoxInfo.js

- **文件路径**: `scripts\M20_Pop_ShopBoxInfo.js`
- **文件大小**: 4856 字符
- **导入数量**: 9
- **导出数量**: 1
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - ... 还有4个导入

### M20_Pop_ShopBuyConfirm.js

- **文件路径**: `scripts\M20_Pop_ShopBuyConfirm.js`
- **文件大小**: 3408 字符
- **导入数量**: 11
- **导出数量**: 1
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - ... 还有6个导入

### M20_PrePare_Activity.js

- **文件路径**: `scripts\M20_PrePare_Activity.js`
- **文件大小**: 5246 字符
- **导入数量**: 15
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - ... 还有10个导入

### M20_PrePare_Equip.js

- **文件路径**: `scripts\M20_PrePare_Equip.js`
- **文件大小**: 7273 字符
- **导入数量**: 11
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - ... 还有6个导入

### M20_PrePare_Fight.js

- **文件路径**: `scripts\M20_PrePare_Fight.js`
- **文件大小**: 19776 字符
- **导入数量**: 13
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - ... 还有8个导入

### M20_PrePare_MenuView.js

- **文件路径**: `scripts\M20_PrePare_MenuView.js`
- **文件大小**: 12490 字符
- **导入数量**: 14
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - ... 还有9个导入

### M20_PrePare_Shop.js

- **文件路径**: `scripts\M20_PrePare_Shop.js`
- **文件大小**: 4608 字符
- **导入数量**: 13
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - ... 还有8个导入

### M20_Shop_HeroItem.js

- **文件路径**: `scripts\M20_Shop_HeroItem.js`
- **文件大小**: 2952 字符
- **导入数量**: 9
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有4个导入

### M20_ShopPartItem_adcoupon.js

- **文件路径**: `scripts\M20_ShopPartItem_adcoupon.js`
- **文件大小**: 1383 字符
- **导入数量**: 5
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2GameUtil <- "GameUtil"
  - $2M20_PartItem <- "M20_PartItem"
  - $2M20_ShopPartItem <- "M20_ShopPartItem"

### M20_ShopPartItem_box.js

- **文件路径**: `scripts\M20_ShopPartItem_box.js`
- **文件大小**: 3988 字符
- **导入数量**: 7
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2UIManager <- "UIManager"
  - ... 还有2个导入

### M20_ShopPartItem_coin.js

- **文件路径**: `scripts\M20_ShopPartItem_coin.js`
- **文件大小**: 1370 字符
- **导入数量**: 5
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2GameUtil <- "GameUtil"
  - $2M20_PartItem <- "M20_PartItem"
  - $2M20_ShopPartItem <- "M20_ShopPartItem"

### M20_ShopPartItem_daily.js

- **文件路径**: `scripts\M20_ShopPartItem_daily.js`
- **文件大小**: 5651 字符
- **导入数量**: 6
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - $2M20_PartItem <- "M20_PartItem"
  - ... 还有1个导入

### M20_ShopPartItem_hero.js

- **文件路径**: `scripts\M20_ShopPartItem_hero.js`
- **文件大小**: 1277 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2GameUtil <- "GameUtil"
  - $2M20_ShopPartItem <- "M20_ShopPartItem"
  - $2M20_Shop_HeroItem <- "M20_Shop_HeroItem"

### M20_ShopPartItem.js

- **文件路径**: `scripts\M20_ShopPartItem.js`
- **文件大小**: 2004 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2ModeBackpackHeroModel <- "ModeBackpackHeroModel"

### M20Equipitem.js

- **文件路径**: `scripts\M20Equipitem.js`
- **文件大小**: 4975 字符
- **导入数量**: 7
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - ... 还有2个导入

### M20EquipitemBlock.js

- **文件路径**: `scripts\M20EquipitemBlock.js`
- **文件大小**: 1490 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2M20Equipitem <- "M20Equipitem"

### M20EquipitemList.js

- **文件路径**: `scripts\M20EquipitemList.js`
- **文件大小**: 2881 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Notifier <- "Notifier"
  - $2M20Equipitem <- "M20Equipitem"

### M20Gooditem.js

- **文件路径**: `scripts\M20Gooditem.js`
- **文件大小**: 1451 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2Manager <- "Manager"

### M20Prop_Gemstone.js

- **文件路径**: `scripts\M20Prop_Gemstone.js`
- **文件大小**: 21006 字符
- **导入数量**: 14
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2SoundCfg <- "SoundCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - ... 还有9个导入

### M20Prop.js

- **文件路径**: `scripts\M20Prop.js`
- **文件大小**: 14918 字符
- **导入数量**: 10
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有5个导入

### M33_FightBuffView.js

- **文件路径**: `scripts\M33_FightBuffView.js`
- **文件大小**: 7346 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2VideoButton <- "VideoButton"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - ... 还有7个导入

### M33_Pop_DiffSelectGeneral.js

- **文件路径**: `scripts\M33_Pop_DiffSelectGeneral.js`
- **文件大小**: 1827 字符
- **导入数量**: 7
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - $2Manager <- "Manager"
  - $2UIManager <- "UIManager"
  - $2EaseScaleTransition <- "EaseScaleTransition"
  - ... 还有2个导入

### M33_Pop_GameEnd.js

- **文件路径**: `scripts\M33_Pop_GameEnd.js`
- **文件大小**: 8137 字符
- **导入数量**: 17
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有12个导入

### M33_Pop_Revive.js

- **文件路径**: `scripts\M33_Pop_Revive.js`
- **文件大小**: 1890 字符
- **导入数量**: 8
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - ... 还有3个导入

### M33_TestBox.js

- **文件路径**: `scripts\M33_TestBox.js`
- **文件大小**: 6324 字符
- **导入数量**: 6
- **导出数量**: 1
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2UIManager <- "UIManager"
  - $2Intersection <- "Intersection"
  - ... 还有1个导入

### Manager.js

- **文件路径**: `scripts\Manager.js`
- **文件大小**: 3311 字符
- **导入数量**: 12
- **导出数量**: 2
- **主要导入**:
  - $2AudioManager <- "AudioManager"
  - $2LoaderAdapter <- "LoaderAdapter"
  - $2VoManager <- "VoManager"
  - $2StorageManager <- "StorageManager"
  - $2UIManager <- "UIManager"
  - ... 还有7个导入

### MathSection.js

- **文件路径**: `scripts\MathSection.js`
- **文件大小**: 1531 字符
- **导入数量**: 0
- **导出数量**: 2

### MBRMonster.js

- **文件路径**: `scripts\MBRMonster.js`
- **文件大小**: 5100 字符
- **导入数量**: 13
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - ... 还有8个导入

### MBRRole.js

- **文件路径**: `scripts\MBRRole.js`
- **文件大小**: 4735 字符
- **导入数量**: 11
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2Buff <- "Buff"
  - ... 还有6个导入

### MCBossState.js

- **文件路径**: `scripts\MCBossState.js`
- **文件大小**: 1646 字符
- **导入数量**: 1
- **导出数量**: 2
- **主要导入**:
  - $2StateMachine <- "StateMachine"

### MCPet.js

- **文件路径**: `scripts\MCPet.js`
- **文件大小**: 1676 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2AlertManager <- "AlertManager"
  - $2DragonBody <- "DragonBody"
  - $2Pet <- "Pet"
  - $2MCBoss <- "MCBoss"

### Md5.js

- **文件路径**: `scripts\Md5.js`
- **文件大小**: 7463 字符
- **导入数量**: 0
- **导出数量**: 1

### md51.js

- **文件路径**: `scripts\md51.js`
- **文件大小**: 5930 字符
- **导入数量**: 0
- **导出数量**: 2

### MinSortList.js

- **文件路径**: `scripts\MinSortList.js`
- **文件大小**: 3393 字符
- **导入数量**: 0
- **导出数量**: 2

### MMGMonster.js

- **文件路径**: `scripts\MMGMonster.js`
- **文件大小**: 4700 字符
- **导入数量**: 9
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Manager <- "Manager"
  - $2Time <- "Time"
  - $2Intersection <- "Intersection"
  - $2GameUtil <- "GameUtil"
  - ... 还有4个导入

### MMGRole.js

- **文件路径**: `scripts\MMGRole.js`
- **文件大小**: 6223 字符
- **导入数量**: 13
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2Time <- "Time"
  - $2Intersection <- "Intersection"
  - ... 还有8个导入

### ModeAllOutAttackModel.js

- **文件路径**: `scripts\ModeAllOutAttackModel.js`
- **文件大小**: 781 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Game <- "Game"

### ModeDragonWarModel.js

- **文件路径**: `scripts\ModeDragonWarModel.js`
- **文件大小**: 1110 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Game <- "Game"

### ModeManGuardsModel.js

- **文件路径**: `scripts\ModeManGuardsModel.js`
- **文件大小**: 772 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Game <- "Game"

### ModePickUpBulletsModel.js

- **文件路径**: `scripts\ModePickUpBulletsModel.js`
- **文件大小**: 784 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Game <- "Game"

### ModeThrowingKnifeModel.js

- **文件路径**: `scripts\ModeThrowingKnifeModel.js`
- **文件大小**: 784 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Game <- "Game"

### ModuleLauncher.js

- **文件路径**: `scripts\ModuleLauncher.js`
- **文件大小**: 2485 字符
- **导入数量**: 21
- **导出数量**: 2
- **主要导入**:
  - $2BottomBarController <- "BottomBarController"
  - $2FightController <- "FightController"
  - $2BuffController <- "BuffController"
  - $2SkillController <- "SkillController"
  - $2LoadingController <- "LoadingController"
  - ... 还有16个导入

### MonstarTideDragon.js

- **文件路径**: `scripts\MonstarTideDragon.js`
- **文件大小**: 5981 字符
- **导入数量**: 13
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2SoundCfg <- "SoundCfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - ... 还有8个导入

### MonsterElite.js

- **文件路径**: `scripts\MonsterElite.js`
- **文件大小**: 1061 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Manager <- "Manager"
  - $2Monster <- "Monster"

### MonsterState.js

- **文件路径**: `scripts\MonsterState.js`
- **文件大小**: 10953 字符
- **导入数量**: 5
- **导出数量**: 2
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2StateMachine <- "StateMachine"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"

### MonsterTidal.js

- **文件路径**: `scripts\MonsterTidal.js`
- **文件大小**: 2992 字符
- **导入数量**: 6
- **导出数量**: 1
- **主要导入**:
  - $2StateMachine <- "StateMachine"
  - $2Game <- "Game"
  - $2SkillManager <- "SkillManager"
  - $2MonsterState <- "MonsterState"
  - $2MonsterTidalState <- "MonsterTidalState"
  - ... 还有1个导入

### MonsterTidalBoss.js

- **文件路径**: `scripts\MonsterTidalBoss.js`
- **文件大小**: 791 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2MonsterTidal <- "MonsterTidal"

### MonsterTidalState.js

- **文件路径**: `scripts\MonsterTidalState.js`
- **文件大小**: 6017 字符
- **导入数量**: 2
- **导出数量**: 2
- **主要导入**:
  - $2StateMachine <- "StateMachine"
  - $2GameUtil <- "GameUtil"

### MonsterTideDefend.js

- **文件路径**: `scripts\MonsterTideDefend.js`
- **文件大小**: 5024 字符
- **导入数量**: 13
- **导出数量**: 1
- **主要导入**:
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2StateMachine <- "StateMachine"
  - $2ListenID <- "ListenID"
  - ... 还有8个导入

### MoreGamesItem.js

- **文件路径**: `scripts\MoreGamesItem.js`
- **文件大小**: 6507 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2GridViewCell <- "GridViewCell"
  - $2VideoButton <- "VideoButton"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有7个导入

### MoreGamesView.js

- **文件路径**: `scripts\MoreGamesView.js`
- **文件大小**: 10613 字符
- **导入数量**: 15
- **导出数量**: 3
- **主要导入**:
  - $2GridView <- "GridView"
  - $2VideoButton <- "VideoButton"
  - $2Cfg <- "Cfg"
  - $2SoundCfg <- "SoundCfg"
  - $2MVC <- "MVC"
  - ... 还有10个导入

### MoveEntity.js

- **文件路径**: `scripts\MoveEntity.js`
- **文件大小**: 3561 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Smoother <- "Smoother"
  - $2BaseEntity <- "BaseEntity"

### MoveImg.js

- **文件路径**: `scripts\MoveImg.js`
- **文件大小**: 1467 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2Game <- "Game"

### MovingBGAssembler.js

- **文件路径**: `scripts\MovingBGAssembler.js`
- **文件大小**: 2293 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2GTSimpleSpriteAssembler2D <- "GTSimpleSpriteAssembler2D"

### MovingBGSprite.js

- **文件路径**: `scripts\MovingBGSprite.js`
- **文件大小**: 2150 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2MovingBGAssembler <- "MovingBGAssembler"

### MTideDefendRmod.js

- **文件路径**: `scripts\MTideDefendRmod.js`
- **文件大小**: 5357 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2Buff <- "Buff"
  - ... 还有7个导入

### MTKRole.js

- **文件路径**: `scripts\MTKRole.js`
- **文件大小**: 6429 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - ... 还有7个导入

### NativeAndroid.js

- **文件路径**: `scripts\NativeAndroid.js`
- **文件大小**: 5612 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2ListenID <- "ListenID"
  - $2Notifier <- "Notifier"
  - $2BaseSdk <- "BaseSdk"

### NetManager.js

- **文件路径**: `scripts\NetManager.js`
- **文件大小**: 2066 字符
- **导入数量**: 1
- **导出数量**: 2
- **主要导入**:
  - $2HttpClient <- "HttpClient"

### NormalTips.js

- **文件路径**: `scripts\NormalTips.js`
- **文件大小**: 1189 字符
- **导入数量**: 0
- **导出数量**: 1

### NotifyCaller.js

- **文件路径**: `scripts\NotifyCaller.js`
- **文件大小**: 1434 字符
- **导入数量**: 2
- **导出数量**: 2
- **主要导入**:
  - $2Log <- "Log"
  - $2CallID <- "CallID"

### NotifyListener.js

- **文件路径**: `scripts\NotifyListener.js`
- **文件大小**: 5287 字符
- **导入数量**: 3
- **导出数量**: 3
- **主要导入**:
  - $2Log <- "Log"
  - $2ListenID <- "ListenID"
  - $2NotifyID <- "NotifyID"

### NPC.js

- **文件路径**: `scripts\NPC.js`
- **文件大小**: 2805 字符
- **导入数量**: 8
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2RewardEvent <- "RewardEvent"
  - ... 还有3个导入

### ObjectPool.js

- **文件路径**: `scripts\ObjectPool.js`
- **文件大小**: 738 字符
- **导入数量**: 0
- **导出数量**: 2

### OrganismBase.js

- **文件路径**: `scripts\OrganismBase.js`
- **文件大小**: 12289 字符
- **导入数量**: 11
- **导出数量**: 3
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2GameatrCfg <- "GameatrCfg"
  - $2Notifier <- "Notifier"
  - $2AlertManager <- "AlertManager"
  - ... 还有6个导入

### Params.js

- **文件路径**: `scripts\Params.js`
- **文件大小**: 1653 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2LocalStorage <- "LocalStorage"

### PayModel.js

- **文件路径**: `scripts\PayModel.js`
- **文件大小**: 684 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $1$2MVC <- "MVC"

### Pet.js

- **文件路径**: `scripts\Pet.js`
- **文件大小**: 5235 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2StateMachine <- "StateMachine"
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - ... 还有7个导入

### PetState.js

- **文件路径**: `scripts\PetState.js`
- **文件大小**: 5491 字符
- **导入数量**: 5
- **导出数量**: 2
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Notifier <- "Notifier"
  - $2StateMachine <- "StateMachine"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"

### Pool.js

- **文件路径**: `scripts\Pool.js`
- **文件大小**: 7542 字符
- **导入数量**: 1
- **导出数量**: 5
- **主要导入**:
  - $2Time <- "Time"

### PoolArray.js

- **文件路径**: `scripts\PoolArray.js`
- **文件大小**: 1441 字符
- **导入数量**: 0
- **导出数量**: 2

### Property.js

- **文件路径**: `scripts\Property.js`
- **文件大小**: 3043 字符
- **导入数量**: 0
- **导出数量**: 1

### QuadTree.js

- **文件路径**: `scripts\QuadTree.js`
- **文件大小**: 4080 字符
- **导入数量**: 0
- **导出数量**: 2

### Random.js

- **文件路径**: `scripts\Random.js`
- **文件大小**: 1491 字符
- **导入数量**: 0
- **导出数量**: 1

### RBadgePoint.js

- **文件路径**: `scripts\RBadgePoint.js`
- **文件大小**: 2720 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2RBadgeModel <- "RBadgeModel"

### RecordVo.js

- **文件路径**: `scripts\RecordVo.js`
- **文件大小**: 4808 字符
- **导入数量**: 6
- **导出数量**: 2
- **主要导入**:
  - $2StorageID <- "StorageID"
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2Manager <- "Manager"
  - $2Time <- "Time"
  - ... 还有1个导入

### RedPointTree.js

- **文件路径**: `scripts\RedPointTree.js`
- **文件大小**: 3022 字符
- **导入数量**: 0
- **导出数量**: 1

### ReportQueue.js

- **文件路径**: `scripts\ReportQueue.js`
- **文件大小**: 1059 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2LocalStorage <- "LocalStorage"

### Request.js

- **文件路径**: `scripts\Request.js`
- **文件大小**: 594 字符
- **导入数量**: 0
- **导出数量**: 1

### ResKeeper.js

- **文件路径**: `scripts\ResKeeper.js`
- **文件大小**: 931 字符
- **导入数量**: 0
- **导出数量**: 1

### Role.js

- **文件路径**: `scripts\Role.js`
- **文件大小**: 8457 字符
- **导入数量**: 15
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Cfg <- "Cfg"
  - $2GameatrCfg <- "GameatrCfg"
  - $2SoundCfg <- "SoundCfg"
  - $2Notifier <- "Notifier"
  - ... 还有10个导入

### RoleState.js

- **文件路径**: `scripts\RoleState.js`
- **文件大小**: 4579 字符
- **导入数量**: 4
- **导出数量**: 2
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2StateMachine <- "StateMachine"
  - $2ListenID <- "ListenID"
  - $2GameUtil <- "GameUtil"

### SdkLauncher.js

- **文件路径**: `scripts\SdkLauncher.js`
- **文件大小**: 9982 字符
- **导入数量**: 9
- **导出数量**: 2
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2Time <- "Time"
  - ... 还有4个导入

### SelectAlert.js

- **文件路径**: `scripts\SelectAlert.js`
- **文件大小**: 3586 字符
- **导入数量**: 5
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Pop <- "Pop"
  - $2GameSeting <- "GameSeting"
  - $2Manager <- "Manager"
  - $2EaseScaleTransition <- "EaseScaleTransition"

### SelectAlertAdapter.js

- **文件路径**: `scripts\SelectAlertAdapter.js`
- **文件大小**: 796 字符
- **导入数量**: 0
- **导出数量**: 2

### SettingModel.js

- **文件路径**: `scripts\SettingModel.js`
- **文件大小**: 2122 字符
- **导入数量**: 6
- **导出数量**: 1
- **主要导入**:
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - $2StorageID <- "StorageID"
  - ... 还有1个导入

### ShareButton.js

- **文件路径**: `scripts\ShareButton.js`
- **文件大小**: 5546 字符
- **导入数量**: 8
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2SoundCfg <- "SoundCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - ... 还有3个导入

### ShopModel.js

- **文件路径**: `scripts\ShopModel.js`
- **文件大小**: 4139 字符
- **导入数量**: 7
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2Time <- "Time"
  - $2RecordVo <- "RecordVo"
  - ... 还有2个导入

### Show.js

- **文件路径**: `scripts\Show.js`
- **文件大小**: 230 字符
- **导入数量**: 0
- **导出数量**: 1

### SkeletonBullet.js

- **文件路径**: `scripts\SkeletonBullet.js`
- **文件大小**: 1017 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2Bullet <- "Bullet"

### SkillManager.js

- **文件路径**: `scripts\SkillManager.js`
- **文件大小**: 8269 字符
- **导入数量**: 7
- **导出数量**: 2
- **主要导入**:
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Notifier <- "Notifier"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - ... 还有2个导入

### SkillModel.js

- **文件路径**: `scripts\SkillModel.js`
- **文件大小**: 5897 字符
- **导入数量**: 5
- **导出数量**: 1
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"

### Smoother.js

- **文件路径**: `scripts\Smoother.js`
- **文件大小**: 861 字符
- **导入数量**: 0
- **导出数量**: 2

### SteeringBehaviors.js

- **文件路径**: `scripts\SteeringBehaviors.js`
- **文件大小**: 19965 字符
- **导入数量**: 0
- **导出数量**: 1

### StorageSync.js

- **文件路径**: `scripts\StorageSync.js`
- **文件大小**: 520 字符
- **导入数量**: 0
- **导出数量**: 1

### SwitchVo.js

- **文件路径**: `scripts\SwitchVo.js`
- **文件大小**: 6320 字符
- **导入数量**: 1
- **导出数量**: 2
- **主要导入**:
  - $2TestController <- "TestController"

### TConfig.js

- **文件路径**: `scripts\TConfig.js`
- **文件大小**: 3000 字符
- **导入数量**: 0
- **导出数量**: 2

### TestItem.js

- **文件路径**: `scripts\TestItem.js`
- **文件大小**: 2523 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2Manager <- "Manager"
  - $2AlertManager <- "AlertManager"
  - $2Game <- "Game"

### TestModel.js

- **文件路径**: `scripts\TestModel.js`
- **文件大小**: 6383 字符
- **导入数量**: 20
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Pool <- "Pool"
  - $2Notifier <- "Notifier"
  - ... 还有15个导入

### ThrowBullet.js

- **文件路径**: `scripts\ThrowBullet.js`
- **文件大小**: 1902 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2Game <- "Game"
  - $2BulletBase <- "BulletBase"

### Time.js

- **文件路径**: `scripts\Time.js`
- **文件大小**: 7122 字符
- **导入数量**: 5
- **导出数量**: 3
- **主要导入**:
  - $2Notifier <- "Notifier"
  - $2NotifyID <- "NotifyID"
  - $2Watcher <- "Watcher"
  - $2ObjectPool <- "ObjectPool"
  - $2MinSortList <- "MinSortList"

### TimeManage.js

- **文件路径**: `scripts\TimeManage.js`
- **文件大小**: 2749 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2function <- "function"
  - $2LocalStorage <- "LocalStorage"

### TornadoBullet.js

- **文件路径**: `scripts\TornadoBullet.js`
- **文件大小**: 757 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2Bullet <- "Bullet"

### TrackBullet.js

- **文件路径**: `scripts\TrackBullet.js`
- **文件大小**: 1845 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2Bullet <- "Bullet"

### TrackItem.js

- **文件路径**: `scripts\TrackItem.js`
- **文件大小**: 4306 字符
- **导入数量**: 4
- **导出数量**: 1
- **主要导入**:
  - $2Manager <- "Manager"
  - $2GameUtil <- "GameUtil"
  - $2Game <- "Game"
  - $2TrackManger <- "TrackManger"

### ttPostbackCtl.js

- **文件路径**: `scripts\ttPostbackCtl.js`
- **文件大小**: 1412 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2SdkConfig <- "SdkConfig"
  - $2index <- "index"
  - $2Report <- "Report"

### UILauncher.js

- **文件路径**: `scripts\UILauncher.js`
- **文件大小**: 447 字符
- **导入数量**: 3
- **导出数量**: 2
- **主要导入**:
  - $2UIManager <- "UIManager"
  - $2MVC <- "MVC"
  - $2Manager <- "Manager"

### Vehicle.js

- **文件路径**: `scripts\Vehicle.js`
- **文件大小**: 1657 字符
- **导入数量**: 2
- **导出数量**: 1
- **主要导入**:
  - $2OrganismBase <- "OrganismBase"
  - $2SteeringBehaviors <- "SteeringBehaviors"

### VideoButton.js

- **文件路径**: `scripts\VideoButton.js`
- **文件大小**: 7053 字符
- **导入数量**: 11
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2SoundCfg <- "SoundCfg"
  - $2MVC <- "MVC"
  - $2Notifier <- "Notifier"
  - ... 还有6个导入

### VideoIcon.js

- **文件路径**: `scripts\VideoIcon.js`
- **文件大小**: 1810 字符
- **导入数量**: 5
- **导出数量**: 1
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2CurrencyConfigCfg <- "CurrencyConfigCfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"

### VisibleComponent.js

- **文件路径**: `scripts\VisibleComponent.js`
- **文件大小**: 1261 字符
- **导入数量**: 1
- **导出数量**: 1
- **主要导入**:
  - $2SdkConfig <- "SdkConfig"

### VoManager.js

- **文件路径**: `scripts\VoManager.js`
- **文件大小**: 2676 字符
- **导入数量**: 7
- **导出数量**: 2
- **主要导入**:
  - $2StorageID <- "StorageID"
  - $2Manager <- "Manager"
  - $2Time <- "Time"
  - $2SdkConfig <- "SdkConfig"
  - $2GameUtil <- "GameUtil"
  - ... 还有2个导入

### WallBase.js

- **文件路径**: `scripts\WallBase.js`
- **文件大小**: 880 字符
- **导入数量**: 3
- **导出数量**: 1
- **主要导入**:
  - $2BulletBase <- "BulletBase"
  - $2BaseEntity <- "BaseEntity"
  - $2OrganismBase <- "OrganismBase"

### Watcher.js

- **文件路径**: `scripts\Watcher.js`
- **文件大小**: 2936 字符
- **导入数量**: 2
- **导出数量**: 2
- **主要导入**:
  - $2BaseEntity <- "BaseEntity"
  - $2Log <- "Log"

### Weather.js

- **文件路径**: `scripts\Weather.js`
- **文件大小**: 6220 字符
- **导入数量**: 12
- **导出数量**: 1
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有7个导入

## 📁 全局函数文件 (2个)

### cc_language.js

- **文件路径**: `scripts\cc_language.js`
- **文件大小**: 9152 字符
- **导入数量**: 0
- **导出数量**: 0
- **全局函数**: 1个
  - window.initlang

### IOSSdk.js

- **文件路径**: `scripts\IOSSdk.js`
- **文件大小**: 5658 字符
- **导入数量**: 6
- **导出数量**: 1
- **全局函数**: 2个
  - window.iOSSendMsg
  - window.iOSBuySendMsg
- **主要导入**:
  - $2CallID <- "CallID"
  - $2GameSeting <- "GameSeting"
  - $2ListenID <- "ListenID"
  - $2Notifier <- "Notifier"
  - $2Manager <- "Manager"
  - ... 还有1个导入

## 📁 混合类型文件 (11个)

### AlertManager.js

- **文件路径**: `scripts\AlertManager.js`
- **文件大小**: 9881 字符
- **导入数量**: 10
- **导出数量**: 4
- **类定义**: 1个
  - AlertManager (static_class)
- **枚举定义**: 2个
  - 枚举1: COMMON, SELECT
  - 枚举2: Tips, NotEnough
- **主要导入**:
  - $2Cfg <- "Cfg"
  - $2MVC <- "MVC"
  - $2Manager <- "Manager"
  - $2UIManager <- "UIManager"
  - $2GameUtil <- "GameUtil"
  - ... 还有5个导入

### BaseNet.js

- **文件路径**: `scripts\BaseNet.js`
- **文件大小**: 5755 字符
- **导入数量**: 0
- **导出数量**: 4
- **类定义**: 1个
  - BaseNet (static_class)
- **枚举定义**: 2个
  - 枚举1: GetIpApi
  - 枚举2: BMS_LAUNCH_CONFIG, BMS_SHARE_CONFIG, BMS_TOFU_CONFIG, BMS_SIGN_IN_WX, BMS_SIGN_IN_BD, BMS_SIGN_IN_QQ...

### CurrencyConfigCfg.js

- **文件路径**: `scripts\CurrencyConfigCfg.js`
- **文件大小**: 3415 字符
- **导入数量**: 1
- **导出数量**: 3
- **类定义**: 1个
  - CurrencyConfigCfgReader (class)
- **枚举定义**: 1个
  - 枚举1: Energy, Coin, Crystal, GOLD, Vedio, Mana, Amethyst, Amethyst_In, Box, Equipfragments, Diamond, BEqui...
- **主要导入**:
  - $2TConfig <- "TConfig"

### DropConfigCfg.js

- **文件路径**: `scripts\DropConfigCfg.js`
- **文件大小**: 885 字符
- **导入数量**: 1
- **导出数量**: 3
- **类定义**: 1个
  - DropConfigCfgReader (class)
- **枚举定义**: 1个
  - 枚举1: ExpGoods, AddHpGoods, PowerRedGoods, BabyBox, ExpGoodsA, CiTie, NCryStal, NCoin, NAmethyst
- **主要导入**:
  - $2TConfig <- "TConfig"

### Game.js

- **文件路径**: `scripts\Game.js`
- **文件大小**: 31307 字符
- **导入数量**: 26
- **导出数量**: 3
- **类定义**: 2个
  - o (static_object)
  - B (static_object)
- **枚举定义**: 1个
  - 枚举1: NONE, START, PAUSE, NONE, ROUGUELIKE, TOWER, CATGAME, HIDEGAME, MOYUTOWER, BACKPACKHERO, THROWINGKNI...
- **主要导入**:
  - $2CallID <- "CallID"
  - $2Cfg <- "Cfg"
  - $2Notifier <- "Notifier"
  - $2ListenID <- "ListenID"
  - $2Manager <- "Manager"
  - ... 还有21个导入

### GameatrCfg.js

- **文件路径**: `scripts\GameatrCfg.js`
- **文件大小**: 4511 字符
- **导入数量**: 1
- **导出数量**: 3
- **类定义**: 1个
  - GameatrCfgReader (class)
- **枚举定义**: 1个
  - 枚举1: hp, recover, roleatk, cirtdam, cirtrate, hitrate, dodge, movespeed, exp, itemarea, roledef, invincib...
- **主要导入**:
  - $2TConfig <- "TConfig"

### GameSettingCfg.js

- **文件路径**: `scripts\GameSettingCfg.js`
- **文件大小**: 659 字符
- **导入数量**: 1
- **导出数量**: 3
- **类定义**: 1个
  - GameSettingCfgReader (class)
- **枚举定义**: 1个
  - 枚举1: tdtime, tdmonappeardis
- **主要导入**:
  - $2TConfig <- "TConfig"

### Global.js

- **文件路径**: `scripts\Global.js`
- **文件大小**: 1088 字符
- **导入数量**: 0
- **导出数量**: 0
- **类定义**: 1个
  - n (static_object)
- **全局函数**: 4个
  - window.isNullOrEmpty
  - window.copy
  - window.toArray
  - window.GameDeepCopy

### RewardEvent.js

- **文件路径**: `scripts\RewardEvent.js`
- **文件大小**: 4796 字符
- **导入数量**: 8
- **导出数量**: 2
- **类定义**: 1个
  - n (static_object)
- **枚举定义**: 1个
  - 枚举1: Not, LvUPSkill, DroppedBuff, Skill, SkillBuff, Buff, Role, Pet, RandomBuild_998, RandomBuild_999, Or...
- **主要导入**:
  - $2ListenID <- "ListenID"
  - $2Notifier <- "Notifier"
  - $2GameUtil <- "GameUtil"
  - $2NodePool <- "NodePool"
  - $2Game <- "Game"
  - ... 还有3个导入

### SoundCfg.js

- **文件路径**: `scripts\SoundCfg.js`
- **文件大小**: 3601 字符
- **导入数量**: 1
- **导出数量**: 3
- **类定义**: 1个
  - SoundCfgReader (class)
- **枚举定义**: 1个
  - 枚举1: bgm_bulletrebound, bgm_dragon, bgm_lobby, bgm_battle, bgm_boss, button_click, reward, skill_zj, skil...
- **主要导入**:
  - $2TConfig <- "TConfig"

### TwoDLayoutObject.js

- **文件路径**: `scripts\TwoDLayoutObject.js`
- **文件大小**: 3978 字符
- **导入数量**: 1
- **导出数量**: 4
- **类定义**: 1个
  - TwoDLayoutObject (class)
- **枚举定义**: 2个
  - 枚举1: LEFT, RIGHT, CENTER
  - 枚举2: CENTER, TOP, BOTTOM
- **主要导入**:
  - $2LayoutObject <- "LayoutObject"

## 📁 空文件 (1个)

### CCNode.js

- **文件路径**: `scripts\CCNode.js`
- **文件大小**: 11431 字符
- **导入数量**: 0
- **导出数量**: 0
