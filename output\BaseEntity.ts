import FCollider from "./FCollider";
import { Game } from "./Game";
import { Watcher } from "./Watcher";

/**
 * Camp type enumeration for entity allegiance
 */
export enum CampType {
    Not = 0,
    One = 1,
    Two = 2,
    Three = 3
}

/**
 * Entity type enumeration using bit flags
 */
export enum EntityType {
    DefauleEntity = 1 << 0,  // 1
    Decorate = 1 << 1,       // 2
    Role = 1 << 2,           // 4
    Monster = 1 << 3,        // 8
    Neutrality = 1 << 4,     // 16
    Bomb = 1 << 5,           // 32
    Bullet = 1 << 6,         // 64
    Goods = 1 << 7,          // 128
    Effect = 1 << 8,         // 256
    Npc = 1 << 9,            // 512
    Pet = 1 << 10            // 1024
}

/**
 * Collider scale settings interface
 */
export interface ColliderScaleSet {
    w: number;
    h: number;
}

const { ccclass, property } = cc._decorator;

/**
 * Base entity class for all game objects
 * Provides common functionality for entities in the game world
 */
@ccclass
export default class BaseEntity extends cc.Component {
    static nextVaildID: number = 1;

    // Private properties
    private _id: number = -1;
    private _isDead: boolean = false;
    private _rTime: number = 0;
    private _isActive: boolean = false;
    private _isInit: boolean = false;
    private _campType: CampType = CampType.Not;
    private _entityType: EntityType = EntityType.DefauleEntity;
    private _radius: number = 32;
    private _haedPosition: cc.Vec2 = cc.v2();
    private _bodyPosition: cc.Vec2 = cc.v2();
    private _position: cc.Vec2 = cc.v2();
    private _isTag: boolean = false;
    private _horDir: number = 1;

    // Public properties
    public removeTime: number = 0;
    public collider: FCollider | null = null;
    public colliderScaleSet: ColliderScaleSet = { w: 1, h: 1 };
    public atkCamp: CampType = CampType.Not;
    public roleDir: number = 1;

    /**
     * Get entity ID
     */
    get ID(): number {
        return this._id;
    }

    /**
     * Get/Set dead state
     */
    get isDead(): boolean {
        return this._isDead;
    }

    set isDead(value: boolean) {
        this._isDead = value;
        this._rTime = 0;
        this.collider?.setActive(!value);
    }

    /**
     * Get/Set active state
     */
    get isActive(): boolean {
        return this._isActive && !this.isDead && this.isValid;
    }

    set isActive(value: boolean) {
        this._isActive = value;
    }

    /**
     * Get/Set camp type
     */
    get campType(): CampType {
        return this._campType;
    }

    set campType(value: CampType) {
        this._campType = value;
        this.atkCamp = value === CampType.Two ? CampType.One : CampType.Two;
    }

    /**
     * Get/Set entity type
     */
    get entityType(): EntityType {
        return this._entityType;
    }

    set entityType(value: EntityType) {
        this._entityType = value;
    }

    /**
     * Get/Set radius
     */
    get radius(): number {
        return this._radius;
    }

    set radius(value: number) {
        this._radius = value;
    }

    /**
     * Get head position (position + head offset)
     */
    get haedPosition(): cc.Vec2 {
        return this.position.add(this._haedPosition);
    }

    /**
     * Get body position (position + body offset)
     */
    get bodyPosition(): cc.Vec2 {
        return this.position.add(this._bodyPosition);
    }

    /**
     * Get current position
     */
    get position(): cc.Vec2 {
        cc.Vec2.set(this._position, this.node.x, this.node.y);
        return this._position;
    }

    /**
     * Get game manager reference
     */
    get game() {
        return Game.mgr;
    }

    /**
     * Get node scale
     */
    get scale(): number {
        return this.node.scale;
    }

    /**
     * Get node angle
     */
    get angle(): number {
        return this.node.angle;
    }

    /**
     * Get/Set tag state
     */
    get isTag(): boolean {
        return this._isTag;
    }

    set isTag(value: boolean) {
        this._isTag = value;
    }

    /**
     * Get setting scale (override in subclasses)
     */
    get settingScale(): number {
        return 1;
    }

    /**
     * Get/Set horizontal direction
     */
    get horDir(): number {
        return this._horDir;
    }

    set horDir(value: number) {
        this._horDir = value;
        if (this.node && this.node.children[0]) {
            this.node.children[0].scaleX = this._horDir * Math.abs(this.node.children[0].scaleX) * this.roleDir;
        }
    }

    /**
     * Initialize entity
     */
    init(): void {
        this._id = BaseEntity.nextVaildID++;
        this._isInit = true;
        this._isDead = false;
        this._isActive = true;
        this.radius = this.node.width / 2;

        if (this.entityType !== EntityType.Bullet) {
            this.delayByGame(() => {
                this.collider?.setActive(true);
            });
        }
    }

    /**
     * Remove entity from update loop
     */
    removeEntityToUpdate(): void {
        this.unuse();
        this.game?.pushToDestroyList(this);
    }

    /**
     * Called when entity is returned to pool
     */
    unuse(): void {
        this.node?.targetOff(this.node);
        this.node?.setActive(false);
        this.cleanEvent();
    }

    /**
     * Called when entity is retrieved from pool
     */
    reuse(): void {
        this.node.active = true;
    }

    /**
     * Set entity position
     */
    setPosition(pos: cc.Vec2): void {
        if (Number.isFinite(pos.x) && Number.isFinite(pos.y)) {
            this.node.setPosition(pos);
        }
    }

    /**
     * Cocos Creator lifecycle - onLoad
     */
    onLoad(): void {
        if (!this.collider) {
            this.collider = this.getComponent(FCollider);
        }
    }

    /**
     * Update method called every frame
     */
    onUpdate(deltaTime: number): void {
        if (this.isDead) {
            this._rTime += deltaTime;
            if (this._rTime > this.removeTime) {
                this.removeEntityToUpdate();
            }
        }
    }

    /**
     * Handle being hit (override in subclasses)
     */
    behit(): any {
        return null;
    }

    /**
     * Schedule with game speed adjustment
     */
    schedule(callback: Function, ...args: number[]): void {
        // Adjust timing based on game speed
        const adjustedArgs = args.map(arg => arg / this.game.gameSpeed);
        super.schedule(callback, ...adjustedArgs);
    }

    /**
     * Delay execution by game time
     */
    delayByGame(callback: Function, delay: number = 0, repeatCount: number = 1): Watcher | null {
        return this.game.timeDelay.delay(delay, callback, null, this, repeatCount);
    }

    /**
     * Clean up events and timers
     */
    cleanEvent(): void {
        this._isActive = false;
        cc.Tween.stopAllByTarget(this.node);
        this.collider?.setActive(false);
        this.unscheduleAllCallbacks();
    }

    /**
     * Cocos Creator lifecycle - onDestroy
     */
    onDestroy(): void {
        this.cleanEvent();
    }
}
