import LocalStorage from "./LocalStorage";

/**
 * Interface for show parameters
 */
export interface ShowParams {
    [key: string]: any;
}

/**
 * Interface for game parameters
 */
export interface GameParams {
    app_name: string;
    channel: string;
    version: string;
    [key: string]: any;
}

/**
 * Interface for user parameters
 */
export interface UserParams {
    openid: string;
    [key: string]: any;
}

/**
 * Parameter management system
 * Handles show parameters, game parameters, and user parameters
 */
class ParamsManager {
    private _showParams: ShowParams = {};
    private _gameParams: GameParams = {
        app_name: "",
        channel: "",
        version: ""
    };
    private _userParams: UserParams = {
        openid: ""
    };
    private _openidKey: string = "sdk_openid";

    constructor() {
        // Load openid from local storage
        this._userParams.openid = LocalStorage.getItem(this._openidKey) || "";
    }

    /**
     * Set show parameters
     * @param params - Show parameters object
     * @returns The set parameters
     */
    setShowParams(params: ShowParams): ShowParams {
        this._showParams = params;
        return this._showParams;
    }

    /**
     * Get show parameters
     * @param key - Optional key to get specific parameter
     * @returns All parameters or specific parameter value
     */
    getShowParams(): ShowParams;
    getShowParams(key: string): any;
    getShowParams(key: string = ""): ShowParams | any {
        if (key === "") {
            return this._showParams;
        } else {
            return this._showParams[key] !== undefined ? this._showParams[key] : "";
        }
    }

    /**
     * Set game parameters
     * @param params - Game parameters object
     * @returns The set parameters
     */
    setGameParams(params: GameParams): GameParams {
        this._gameParams = params;
        return this._gameParams;
    }

    /**
     * Get game parameters
     * @param key - Optional key to get specific parameter
     * @returns All parameters or specific parameter value
     */
    getGameParams(): GameParams;
    getGameParams(key: string): any;
    getGameParams(key: string = ""): GameParams | any {
        if (key === "") {
            return this._gameParams;
        } else {
            return this._gameParams[key] !== undefined ? this._gameParams[key] : "";
        }
    }

    /**
     * Set user parameters
     * @param params - User parameters object
     * @returns The set parameters
     */
    setUserParams(params: UserParams): UserParams {
        // Save openid to local storage
        const openid = params.openid !== undefined ? params.openid : "";
        LocalStorage.setItem(this._openidKey, openid);
        
        this._userParams = params;
        return this._userParams;
    }

    /**
     * Get user parameters
     * @param key - Optional key to get specific parameter
     * @returns All parameters or specific parameter value
     */
    getUserParams(): UserParams;
    getUserParams(key: string): any;
    getUserParams(key: string = ""): UserParams | any {
        if (key === "") {
            return this._userParams;
        } else {
            return this._userParams[key] !== undefined ? this._userParams[key] : "";
        }
    }

    /**
     * Update a specific show parameter
     * @param key - Parameter key
     * @param value - Parameter value
     */
    updateShowParam(key: string, value: any): void {
        this._showParams[key] = value;
    }

    /**
     * Update a specific game parameter
     * @param key - Parameter key
     * @param value - Parameter value
     */
    updateGameParam(key: string, value: any): void {
        this._gameParams[key] = value;
    }

    /**
     * Update a specific user parameter
     * @param key - Parameter key
     * @param value - Parameter value
     */
    updateUserParam(key: string, value: any): void {
        this._userParams[key] = value;
        
        // If updating openid, save to local storage
        if (key === "openid") {
            LocalStorage.setItem(this._openidKey, value || "");
        }
    }

    /**
     * Clear all show parameters
     */
    clearShowParams(): void {
        this._showParams = {};
    }

    /**
     * Clear all game parameters (reset to defaults)
     */
    clearGameParams(): void {
        this._gameParams = {
            app_name: "",
            channel: "",
            version: ""
        };
    }

    /**
     * Clear all user parameters (reset to defaults)
     */
    clearUserParams(): void {
        this._userParams = {
            openid: ""
        };
        LocalStorage.removeItem(this._openidKey);
    }

    /**
     * Get all parameters as a combined object
     */
    getAllParams(): {
        show: ShowParams;
        game: GameParams;
        user: UserParams;
    } {
        return {
            show: this._showParams,
            game: this._gameParams,
            user: this._userParams
        };
    }

    /**
     * Check if a show parameter exists
     * @param key - Parameter key
     * @returns True if parameter exists
     */
    hasShowParam(key: string): boolean {
        return this._showParams[key] !== undefined;
    }

    /**
     * Check if a game parameter exists
     * @param key - Parameter key
     * @returns True if parameter exists
     */
    hasGameParam(key: string): boolean {
        return this._gameParams[key] !== undefined;
    }

    /**
     * Check if a user parameter exists
     * @param key - Parameter key
     * @returns True if parameter exists
     */
    hasUserParam(key: string): boolean {
        return this._userParams[key] !== undefined;
    }

    /**
     * Get the openid key used for local storage
     */
    get openidKey(): string {
        return this._openidKey;
    }

    /**
     * Set the openid key used for local storage
     * @param key - New openid key
     */
    set openidKey(key: string) {
        this._openidKey = key;
    }
}

// Export singleton instance
export default new ParamsManager();
