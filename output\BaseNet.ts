/**
 * Base URL configuration for different services
 */
export enum BaseUrl {
    ServerDomain = "", // 游戏服务 https://game.zuiqiangyingyu.net
    ServerSourceAPI = "", // 游戏后台 https://api.wanjiazhuhua.com
    GetIpApi = "https://ifconfig.co"
}

/**
 * API endpoint URLs
 */
export enum Url {
    BMS_LAUNCH_CONFIG = "/common/config/info",
    BMS_SHARE_CONFIG = "/common/game/share_list",
    BMS_TOFU_CONFIG = "/common/game/ads",
    BMS_SIGN_IN_WX = "/common/session/sign_in",
    BMS_SIGN_IN_BD = "/common/baidu/sign_in",
    BMS_SIGN_IN_QQ = "/common/qqminiapp/sign_in",
    BMS_SERVER_TIME = "/common/common/time",
    BMS_CARD_SHARE = "/common/share/hit",
    BMS_CARD_SHARE_INFO = "/common/share/info",
    BMS_SHARE_SHOW = "/statistics/share/show",
    BMS_LOGIN_LOG = "/statistics/login_log",
    BMS_GAME = "/statistics/game",
    BMS_AD_SHOW = "/statistics/ad/show",
    BMS_AD_HIT = "/statistics/ad/hit",
    BMS_HINT = "/statistics/hint",
    BMS_IP_IS_ENABLE = "/common/is/is",
    DECODE_DATA = "/common/wechat/decode_data",
    ANTIDIRT = "/common/toutiao/antidirt",
    GETADVINFO = "/api/sdk/yw/adv/getAdvInfo",
    MATERIALSS = "/common/ads/material-ss",
    LOGINCODE = "/common/login-code/check",
    DATA_MULTIGET = "/common/game-data/multi-get",
    DATA_GET = "/common/game-data/get",
    DATA_SAVE = "/common/game-data/save",
    GIFT_RECEIVE_REWARD = "/common/toutiao/gift-receive-reward"
}

/**
 * HTTP request methods
 */
export type HttpMethod = "GET" | "POST";

/**
 * Response data types
 */
export type ResponseType = "json" | "text" | "blob" | "arraybuffer";

/**
 * Standard API response interface
 */
export interface ApiResponse<T = any> {
    code: number;
    msg: string;
    data: T;
}

/**
 * Request parameters interface
 */
export interface RequestParams {
    [key: string]: string | number | boolean;
}

/**
 * Base networking class for HTTP requests
 */
export class BaseNet {
    /**
     * Make an HTTP request
     * @param url - Request URL
     * @param params - Request parameters
     * @param method - HTTP method (GET or POST)
     * @param responseType - Expected response type
     * @returns Promise with response data
     */
    static Request<T = any>(
        url: string, 
        params?: RequestParams, 
        method: HttpMethod = "GET", 
        responseType: ResponseType = "json"
    ): Promise<T> {
        switch (method) {
            case "GET":
                return this.httpGet<T>(url, params, responseType);
            case "POST":
                return this.httpPost<T>(url, params, responseType);
            default:
                throw new Error(`Unsupported HTTP method: ${method}`);
        }
    }

    /**
     * Make a GET request
     * @param url - Request URL
     * @param params - Query parameters
     * @param responseType - Expected response type
     * @returns Promise with response data
     */
    static httpGet<T = any>(
        url: string, 
        params?: RequestParams, 
        responseType: ResponseType = "json"
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // Build query string
            if (params) {
                let queryString = "?";
                for (const key in params) {
                    if (queryString !== "?") {
                        queryString += "&";
                    }
                    queryString += `${key}=${params[key]}`;
                }
                url += queryString;
            }

            console.debug("[HTTP] GET", url);

            // Test mode check
            if ((window as any).wonderSdk?.isTest) {
                resolve({ code: 200, msg: "测试", data: {} } as any);
                return;
            }

            xhr.open("GET", url, true);

            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        this.handleResponse(xhr, responseType, resolve, reject);
                    } else {
                        reject({
                            code: xhr.status,
                            msg: xhr.statusText,
                            data: {}
                        });
                    }
                }
            };

            // Set headers
            this.setRequestHeaders(xhr, responseType);

            // Set response type for binary data
            if (["blob", "arraybuffer", "text"].includes(responseType)) {
                xhr.responseType = responseType as XMLHttpRequestResponseType;
            }

            // Set timeout
            xhr.timeout = 5000;
            xhr.ontimeout = () => {
                reject({
                    code: -1,
                    msg: "网络异常，消息发送超时",
                    data: {}
                });
            };

            xhr.onerror = () => {
                reject({
                    code: -1,
                    msg: "网络异常，消息发送超时",
                    data: {}
                });
            };

            xhr.send();
        });
    }

    /**
     * Make a POST request
     * @param url - Request URL
     * @param params - Request body parameters
     * @param responseType - Expected response type
     * @returns Promise with response data
     */
    static httpPost<T = any>(
        url: string, 
        params?: RequestParams, 
        responseType: ResponseType = "json"
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            console.debug("[HTTP] POST", url);

            // Test mode check
            if ((window as any).wonderSdk?.isTest) {
                resolve({ code: 200, msg: "测试", data: {} } as any);
                return;
            }

            xhr.open("POST", url, true);

            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        this.handleResponse(xhr, responseType, resolve, reject);
                    } else {
                        reject({
                            code: xhr.status,
                            msg: xhr.statusText,
                            data: {}
                        });
                    }
                }
            };

            // Set headers for POST
            xhr.setRequestHeader("content-type", "application/x-www-form-urlencoded");
            if (cc.sys.isNative) {
                xhr.setRequestHeader("Accept-Encoding", "gzip,deflate");
            }

            // Set response type for binary data
            if (["blob", "arraybuffer", "text"].includes(responseType)) {
                xhr.responseType = responseType as XMLHttpRequestResponseType;
            }

            // Encode form data
            let formData: string | undefined;
            if (params) {
                formData = this._EncodeFormData(params);
            }

            // Set timeout
            xhr.timeout = 7000;
            xhr.ontimeout = () => {
                reject({
                    code: -1,
                    msg: "网络异常，消息发送超时",
                    data: {}
                });
            };

            xhr.onerror = () => {
                reject({
                    code: -1,
                    msg: "网络异常，消息发送超时",
                    data: {}
                });
            };

            xhr.send(formData);
        });
    }

    /**
     * Handle XMLHttpRequest response
     */
    private static handleResponse<T>(
        xhr: XMLHttpRequest, 
        responseType: ResponseType, 
        resolve: (value: T) => void, 
        reject: (reason: any) => void
    ): void {
        if (responseType === "text") {
            resolve(xhr.responseText as any);
        } else if (responseType === "json" && typeof xhr.response === "string") {
            if (xhr.response.includes("code") && xhr.response.includes("data")) {
                resolve(JSON.parse(xhr.response));
            } else {
                reject({
                    code: xhr.status,
                    msg: xhr.response,
                    data: {}
                });
            }
        } else {
            resolve(xhr.response);
        }
    }

    /**
     * Set request headers based on response type
     */
    private static setRequestHeaders(xhr: XMLHttpRequest, responseType: ResponseType): void {
        switch (responseType) {
            case "json":
                xhr.setRequestHeader("content-type", "application/json");
                break;
            case "text":
                xhr.setRequestHeader("content-type", "text/plain");
                break;
        }
    }

    /**
     * Encode form data for POST requests
     * @param data - Data to encode
     * @returns URL-encoded string
     */
    private static _EncodeFormData(data: RequestParams): string {
        const pairs: string[] = [];
        const spaceRegex = /%20/g;

        for (const key in data) {
            const value = data[key];
            const encodedPair = encodeURIComponent(key).replace(spaceRegex, "+") + 
                               "=" + 
                               encodeURIComponent(value.toString()).replace(spaceRegex, "+");
            pairs.push(encodedPair);
        }

        return pairs.join("&");
    }
}
