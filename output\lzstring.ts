/**
 * LZ-String compression library
 * TypeScript implementation of LZ-based compression algorithm
 */

// Character sets for different encoding formats
const BASE64_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
const URI_SAFE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$";

// Cache for character mappings
const charMappingCache: { [key: string]: { [char: string]: number } } = {};

/**
 * Get character index from character set
 */
function getCharIndex(charset: string, char: string): number {
    if (!charMappingCache[charset]) {
        charMappingCache[charset] = {};
        for (let i = 0; i < charset.length; i++) {
            charMappingCache[charset][charset.charAt(i)] = i;
        }
    }
    return charMappingCache[charset][char];
}

/**
 * Internal compression function
 */
function _compress(uncompressed: string, bitsPerChar: number, getCharFromInt: (val: number) => string): string {
    if (uncompressed == null) return "";
    
    let i: number;
    let value: number;
    const context_dictionary: { [key: string]: number } = {};
    const context_dictionaryToCreate: { [key: string]: boolean } = {};
    let context_c = "";
    let context_wc = "";
    let context_w = "";
    let context_enlargeIn = 2;
    let context_dictSize = 3;
    let context_numBits = 2;
    const context_data: number[] = [];
    let context_data_val = 0;
    let context_data_position = 0;

    for (let ii = 0; ii < uncompressed.length; ii += 1) {
        context_c = uncompressed.charAt(ii);
        if (!Object.prototype.hasOwnProperty.call(context_dictionary, context_c)) {
            context_dictionary[context_c] = context_dictSize++;
            context_dictionaryToCreate[context_c] = true;
        }

        context_wc = context_w + context_c;
        if (Object.prototype.hasOwnProperty.call(context_dictionary, context_wc)) {
            context_w = context_wc;
        } else {
            if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {
                if (context_w.charCodeAt(0) < 256) {
                    for (i = 0; i < context_numBits; i++) {
                        context_data_val = (context_data_val << 1);
                        if (context_data_position == bitsPerChar - 1) {
                            context_data_position = 0;
                            context_data.push(getCharFromInt(context_data_val));
                            context_data_val = 0;
                        } else {
                            context_data_position++;
                        }
                    }
                    value = context_w.charCodeAt(0);
                    for (i = 0; i < 8; i++) {
                        context_data_val = (context_data_val << 1) | (value & 1);
                        if (context_data_position == bitsPerChar - 1) {
                            context_data_position = 0;
                            context_data.push(getCharFromInt(context_data_val));
                            context_data_val = 0;
                        } else {
                            context_data_position++;
                        }
                        value = value >> 1;
                    }
                } else {
                    value = 1;
                    for (i = 0; i < context_numBits; i++) {
                        context_data_val = (context_data_val << 1) | value;
                        if (context_data_position == bitsPerChar - 1) {
                            context_data_position = 0;
                            context_data.push(getCharFromInt(context_data_val));
                            context_data_val = 0;
                        } else {
                            context_data_position++;
                        }
                        value = 0;
                    }
                    value = context_w.charCodeAt(0);
                    for (i = 0; i < 16; i++) {
                        context_data_val = (context_data_val << 1) | (value & 1);
                        if (context_data_position == bitsPerChar - 1) {
                            context_data_position = 0;
                            context_data.push(getCharFromInt(context_data_val));
                            context_data_val = 0;
                        } else {
                            context_data_position++;
                        }
                        value = value >> 1;
                    }
                }
                context_enlargeIn--;
                if (context_enlargeIn == 0) {
                    context_enlargeIn = Math.pow(2, context_numBits);
                    context_numBits++;
                }
                delete context_dictionaryToCreate[context_w];
            } else {
                value = context_dictionary[context_w];
                for (i = 0; i < context_numBits; i++) {
                    context_data_val = (context_data_val << 1) | (value & 1);
                    if (context_data_position == bitsPerChar - 1) {
                        context_data_position = 0;
                        context_data.push(getCharFromInt(context_data_val));
                        context_data_val = 0;
                    } else {
                        context_data_position++;
                    }
                    value = value >> 1;
                }
            }
            context_enlargeIn--;
            if (context_enlargeIn == 0) {
                context_enlargeIn = Math.pow(2, context_numBits);
                context_numBits++;
            }
            context_dictionary[context_wc] = context_dictSize++;
            context_w = String(context_c);
        }
    }

    if (context_w !== "") {
        if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {
            if (context_w.charCodeAt(0) < 256) {
                for (i = 0; i < context_numBits; i++) {
                    context_data_val = (context_data_val << 1);
                    if (context_data_position == bitsPerChar - 1) {
                        context_data_position = 0;
                        context_data.push(getCharFromInt(context_data_val));
                        context_data_val = 0;
                    } else {
                        context_data_position++;
                    }
                }
                value = context_w.charCodeAt(0);
                for (i = 0; i < 8; i++) {
                    context_data_val = (context_data_val << 1) | (value & 1);
                    if (context_data_position == bitsPerChar - 1) {
                        context_data_position = 0;
                        context_data.push(getCharFromInt(context_data_val));
                        context_data_val = 0;
                    } else {
                        context_data_position++;
                    }
                    value = value >> 1;
                }
            } else {
                value = 1;
                for (i = 0; i < context_numBits; i++) {
                    context_data_val = (context_data_val << 1) | value;
                    if (context_data_position == bitsPerChar - 1) {
                        context_data_position = 0;
                        context_data.push(getCharFromInt(context_data_val));
                        context_data_val = 0;
                    } else {
                        context_data_position++;
                    }
                    value = 0;
                }
                value = context_w.charCodeAt(0);
                for (i = 0; i < 16; i++) {
                    context_data_val = (context_data_val << 1) | (value & 1);
                    if (context_data_position == bitsPerChar - 1) {
                        context_data_position = 0;
                        context_data.push(getCharFromInt(context_data_val));
                        context_data_val = 0;
                    } else {
                        context_data_position++;
                    }
                    value = value >> 1;
                }
            }
            context_enlargeIn--;
            if (context_enlargeIn == 0) {
                context_enlargeIn = Math.pow(2, context_numBits);
                context_numBits++;
            }
            delete context_dictionaryToCreate[context_w];
        } else {
            value = context_dictionary[context_w];
            for (i = 0; i < context_numBits; i++) {
                context_data_val = (context_data_val << 1) | (value & 1);
                if (context_data_position == bitsPerChar - 1) {
                    context_data_position = 0;
                    context_data.push(getCharFromInt(context_data_val));
                    context_data_val = 0;
                } else {
                    context_data_position++;
                }
                value = value >> 1;
            }
        }
        context_enlargeIn--;
        if (context_enlargeIn == 0) {
            context_enlargeIn = Math.pow(2, context_numBits);
            context_numBits++;
        }
    }

    value = 2;
    for (i = 0; i < context_numBits; i++) {
        context_data_val = (context_data_val << 1) | (value & 1);
        if (context_data_position == bitsPerChar - 1) {
            context_data_position = 0;
            context_data.push(getCharFromInt(context_data_val));
            context_data_val = 0;
        } else {
            context_data_position++;
        }
        value = value >> 1;
    }

    while (true) {
        context_data_val = (context_data_val << 1);
        if (context_data_position == bitsPerChar - 1) {
            context_data.push(getCharFromInt(context_data_val));
            break;
        } else {
            context_data_position++;
        }
    }

    return context_data.join('');
}

/**
 * Internal decompression function
 */
function _decompress(length: number, resetValue: number, getNextValue: (index: number) => number): string | null {
    const dictionary: string[] = [];
    let enlargeIn = 4;
    let dictSize = 4;
    let numBits = 3;
    let entry = "";
    const result: string[] = [];
    let i: number;
    let w: string;
    let bits = 0;
    let maxpower: number;
    let power: number;
    let c: string;
    let data = { val: getNextValue(0), position: resetValue, index: 1 };

    for (i = 0; i < 3; i += 1) {
        dictionary[i] = String.fromCharCode(i);
    }

    bits = 0;
    maxpower = Math.pow(2, 2);
    power = 1;
    while (power != maxpower) {
        const resb = data.val & data.position;
        data.position >>= 1;
        if (data.position == 0) {
            data.position = resetValue;
            data.val = getNextValue(data.index++);
        }
        bits |= (resb > 0 ? 1 : 0) * power;
        power <<= 1;
    }

    switch (bits) {
        case 0:
            bits = 0;
            maxpower = Math.pow(2, 8);
            power = 1;
            while (power != maxpower) {
                const resb = data.val & data.position;
                data.position >>= 1;
                if (data.position == 0) {
                    data.position = resetValue;
                    data.val = getNextValue(data.index++);
                }
                bits |= (resb > 0 ? 1 : 0) * power;
                power <<= 1;
            }
            c = String.fromCharCode(bits);
            break;
        case 1:
            bits = 0;
            maxpower = Math.pow(2, 16);
            power = 1;
            while (power != maxpower) {
                const resb = data.val & data.position;
                data.position >>= 1;
                if (data.position == 0) {
                    data.position = resetValue;
                    data.val = getNextValue(data.index++);
                }
                bits |= (resb > 0 ? 1 : 0) * power;
                power <<= 1;
            }
            c = String.fromCharCode(bits);
            break;
        case 2:
            return "";
    }

    dictionary[3] = c!;
    w = c!;
    result.push(c!);

    while (true) {
        if (data.index > length) {
            return "";
        }

        bits = 0;
        maxpower = Math.pow(2, numBits);
        power = 1;
        while (power != maxpower) {
            const resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
                data.position = resetValue;
                data.val = getNextValue(data.index++);
            }
            bits |= (resb > 0 ? 1 : 0) * power;
            power <<= 1;
        }

        switch (c = String.fromCharCode(bits)) {
            case "0":
                bits = 0;
                maxpower = Math.pow(2, 8);
                power = 1;
                while (power != maxpower) {
                    const resb = data.val & data.position;
                    data.position >>= 1;
                    if (data.position == 0) {
                        data.position = resetValue;
                        data.val = getNextValue(data.index++);
                    }
                    bits |= (resb > 0 ? 1 : 0) * power;
                    power <<= 1;
                }

                dictionary[dictSize++] = String.fromCharCode(bits);
                c = String.fromCharCode(dictSize - 1);
                enlargeIn--;
                break;
            case "1":
                bits = 0;
                maxpower = Math.pow(2, 16);
                power = 1;
                while (power != maxpower) {
                    const resb = data.val & data.position;
                    data.position >>= 1;
                    if (data.position == 0) {
                        data.position = resetValue;
                        data.val = getNextValue(data.index++);
                    }
                    bits |= (resb > 0 ? 1 : 0) * power;
                    power <<= 1;
                }
                dictionary[dictSize++] = String.fromCharCode(bits);
                c = String.fromCharCode(dictSize - 1);
                enlargeIn--;
                break;
            case "2":
                return result.join('');
        }

        if (enlargeIn == 0) {
            enlargeIn = Math.pow(2, numBits);
            numBits++;
        }

        if (dictionary[bits]) {
            entry = dictionary[bits];
        } else {
            if (bits === dictSize) {
                entry = w + w.charAt(0);
            } else {
                return null;
            }
        }
        result.push(entry);

        dictionary[dictSize++] = w + entry.charAt(0);
        enlargeIn--;

        w = entry;

        if (enlargeIn == 0) {
            enlargeIn = Math.pow(2, numBits);
            numBits++;
        }
    }
}

/**
 * LZ-String compression and decompression functions
 */
export const LZString = {
    /**
     * Compress string to Base64
     */
    compressToBase64(input: string): string {
        if (input == null) return "";
        const res = _compress(input, 6, (a) => BASE64_CHARS.charAt(a));
        switch (res.length % 4) {
            default:
            case 0:
                return res;
            case 1:
                return res + "===";
            case 2:
                return res + "==";
            case 3:
                return res + "=";
        }
    },

    /**
     * Decompress string from Base64
     */
    decompressFromBase64(input: string): string | null {
        if (input == null) return "";
        if (input == "") return null;
        return _decompress(input.length, 32, (index) => getCharIndex(BASE64_CHARS, input.charAt(index)));
    },

    /**
     * Compress string to UTF16
     */
    compressToUTF16(input: string): string {
        if (input == null) return "";
        return _compress(input, 15, (a) => String.fromCharCode(a + 32)) + " ";
    },

    /**
     * Decompress string from UTF16
     */
    decompressFromUTF16(compressed: string): string | null {
        if (compressed == null) return "";
        if (compressed == "") return null;
        return _decompress(compressed.length, 16384, (index) => compressed.charCodeAt(index) - 32);
    },

    /**
     * Compress string to Uint8Array
     */
    compressToUint8Array(uncompressed: string): Uint8Array {
        const compressed = this.compress(uncompressed);
        const buf = new Uint8Array(compressed.length * 2);

        for (let i = 0, TotalLen = compressed.length; i < TotalLen; i++) {
            const current_value = compressed.charCodeAt(i);
            buf[i * 2] = current_value >>> 8;
            buf[i * 2 + 1] = current_value % 256;
        }
        return buf;
    },

    /**
     * Decompress from Uint8Array
     */
    decompressFromUint8Array(compressed: Uint8Array): string | null {
        if (compressed === null || compressed === undefined) {
            return this.decompress(compressed as any);
        }

        const buf = new Array(compressed.length / 2);
        for (let i = 0, TotalLen = buf.length; i < TotalLen; i++) {
            buf[i] = compressed[i * 2] * 256 + compressed[i * 2 + 1];
        }

        const result: string[] = [];
        buf.forEach((c) => {
            result.push(String.fromCharCode(c));
        });
        return this.decompress(result.join(''));
    },

    /**
     * Compress string to encoded URI component
     */
    compressToEncodedURIComponent(input: string): string {
        if (input == null) return "";
        return _compress(input, 6, (a) => URI_SAFE_CHARS.charAt(a));
    },

    /**
     * Decompress from encoded URI component
     */
    decompressFromEncodedURIComponent(input: string): string | null {
        if (input == null) return "";
        if (input == "") return null;
        input = input.replace(/ /g, "+");
        return _decompress(input.length, 32, (index) => getCharIndex(URI_SAFE_CHARS, input.charAt(index)));
    },

    /**
     * Basic compress function
     */
    compress(uncompressed: string): string {
        return _compress(uncompressed, 16, String.fromCharCode);
    },

    /**
     * Basic decompress function
     */
    decompress(compressed: string): string | null {
        if (compressed == null) return "";
        if (compressed == "") return null;
        return _decompress(compressed.length, 32768, (index) => compressed.charCodeAt(index));
    }
};

// Export individual functions for convenience
export const compressToBase64 = LZString.compressToBase64;
export const decompressFromBase64 = LZString.decompressFromBase64;
export const compressToUTF16 = LZString.compressToUTF16;
export const decompressFromUTF16 = LZString.decompressFromUTF16;
export const compressToUint8Array = LZString.compressToUint8Array;
export const decompressFromUint8Array = LZString.decompressFromUint8Array;
export const compressToEncodedURIComponent = LZString.compressToEncodedURIComponent;
export const decompressFromEncodedURIComponent = LZString.decompressFromEncodedURIComponent;
export const compress = LZString.compress;
export const decompress = LZString.decompress;

// Default export
export default LZString;
