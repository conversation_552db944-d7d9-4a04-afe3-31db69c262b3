/**
 * Constructor function type for creating new objects
 */
export type ObjectConstructor<T> = () => T;

/**
 * Callback function type for when objects are popped from pool
 */
export type OnPopCallback<T> = (obj: T) => void;

/**
 * Callback function type for when objects are pushed to pool
 */
export type OnPushCallback<T> = (obj: T) => void;

/**
 * Generic object pool for efficient object reuse
 * Reduces garbage collection by reusing objects instead of creating new ones
 */
export class ObjectPool<T> {
    private _ctor: ObjectConstructor<T>;
    private _queue: T[] = [];
    private _onPop: OnPopCallback<T> | null;
    private _onPush: OnPushCallback<T> | null;

    /**
     * Create a new object pool
     * @param constructor - Function to create new objects when pool is empty
     * @param onPop - Optional callback when object is retrieved from pool
     * @param onPush - Optional callback when object is returned to pool
     */
    constructor(
        constructor: ObjectConstructor<T>,
        onPop: OnPopCallback<T> | null = null,
        onPush: OnPushCallback<T> | null = null
    ) {
        this._ctor = constructor;
        this._onPop = onPop;
        this._onPush = onPush;
    }

    /**
     * Get an object from the pool
     * Creates a new object if pool is empty
     * @returns Object from pool or newly created object
     */
    pop(): T {
        let obj: T;
        
        if (this._queue.length > 0) {
            obj = this._queue.shift()!;
        } else {
            obj = this._ctor();
        }

        if (this._onPop != null) {
            this._onPop(obj);
        }

        return obj;
    }

    /**
     * Return an object to the pool
     * @param obj - Object to return to pool
     */
    push(obj: T): void {
        if (this._onPush != null) {
            this._onPush(obj);
        }
        this._queue.push(obj);
    }

    /**
     * Clear all objects from the pool
     */
    clear(): void {
        this._queue = [];
    }

    /**
     * Get the current size of the pool
     * @returns Number of objects currently in the pool
     */
    get size(): number {
        return this._queue.length;
    }

    /**
     * Check if the pool is empty
     * @returns True if pool has no objects
     */
    get isEmpty(): boolean {
        return this._queue.length === 0;
    }

    /**
     * Peek at the next object without removing it
     * @returns Next object in queue or null if empty
     */
    peek(): T | null {
        return this._queue.length > 0 ? this._queue[0] : null;
    }

    /**
     * Pre-fill the pool with objects
     * @param count - Number of objects to create and add to pool
     */
    preload(count: number): void {
        for (let i = 0; i < count; i++) {
            const obj = this._ctor();
            this._queue.push(obj);
        }
    }

    /**
     * Drain the pool to a specific size
     * @param targetSize - Target size for the pool
     */
    drain(targetSize: number): void {
        while (this._queue.length > targetSize) {
            this._queue.pop();
        }
    }

    /**
     * Get all objects currently in the pool (for debugging)
     * @returns Array of all pooled objects
     */
    getAllObjects(): T[] {
        return [...this._queue];
    }

    /**
     * Set new callbacks for pop/push operations
     * @param onPop - New pop callback
     * @param onPush - New push callback
     */
    setCallbacks(onPop: OnPopCallback<T> | null, onPush: OnPushCallback<T> | null): void {
        this._onPop = onPop;
        this._onPush = onPush;
    }

    /**
     * Create a pool with automatic reset functionality
     * Useful for objects that need to be reset when returned to pool
     * @param constructor - Constructor function
     * @param resetFunction - Function to reset object state
     * @returns New ObjectPool with reset functionality
     */
    static createWithReset<T>(
        constructor: ObjectConstructor<T>,
        resetFunction: (obj: T) => void
    ): ObjectPool<T> {
        return new ObjectPool<T>(
            constructor,
            null, // onPop
            resetFunction // onPush - reset when returned to pool
        );
    }

    /**
     * Create a pool with initialization functionality
     * Useful for objects that need setup when retrieved from pool
     * @param constructor - Constructor function
     * @param initFunction - Function to initialize object
     * @returns New ObjectPool with initialization functionality
     */
    static createWithInit<T>(
        constructor: ObjectConstructor<T>,
        initFunction: (obj: T) => void
    ): ObjectPool<T> {
        return new ObjectPool<T>(
            constructor,
            initFunction, // onPop - initialize when retrieved
            null // onPush
        );
    }

    /**
     * Create a pool with both initialization and reset functionality
     * @param constructor - Constructor function
     * @param initFunction - Function to initialize object when popped
     * @param resetFunction - Function to reset object when pushed
     * @returns New ObjectPool with full lifecycle management
     */
    static createWithLifecycle<T>(
        constructor: ObjectConstructor<T>,
        initFunction: (obj: T) => void,
        resetFunction: (obj: T) => void
    ): ObjectPool<T> {
        return new ObjectPool<T>(
            constructor,
            initFunction, // onPop
            resetFunction // onPush
        );
    }
}
