import { Cfg } from "./Cfg";
import BaseEntity, { EntityType, CampType } from "./BaseEntity";
import { PropertyVo } from "./PropertyVo";
import { IPoolable } from "./Pool";

/**
 * Bullet configuration interface
 */
export interface BulletConfig {
    prefab: string;
    soundhitId: number;
    [key: string]: any;
}

/**
 * Skill configuration interface
 */
export interface SkillConfig {
    object: number;
    [key: string]: any;
}

/**
 * Skill interface
 */
export interface Skill {
    skillMainID: number;
    cutVo: SkillConfig;
    getOwner(): BaseEntity;
}

const { ccclass } = cc._decorator;

/**
 * Bullet value object containing all bullet-related data
 * Used for projectile entities in the game
 */
@ccclass("BulletVo")
export class BulletVo implements IPoolable {
    // Core bullet properties
    private _hurt: PropertyVo.Hurt;
    private _bulletId: number = 0;
    private _belong: EntityType = EntityType.Role;
    private _campType: CampType = CampType.Not;

    // Bullet behavior properties
    public crossNum: number = 0;
    public isHitBack: boolean = true;
    public hitBack: number = 0;
    public lotId: number = 0;
    public ignore: number[] = [];
    public atkCamp: CampType[] = [];

    // Position and movement
    public startPos: cc.Vec2 = cc.v2();
    public targetPos: cc.Vec2 = cc.v2();
    public shootDir: cc.Vec2 = cc.v2();
    public speed: number = 500;

    // Bullet properties
    public lifeTime: number = 2;
    public isForever: boolean = false;
    public bulletType: number = 2;
    public range: number = 1;
    public effectType: number = 0;
    public scale: number = 1;
    public radius: number | null = null;
    public size: number = 0;

    // Related objects
    public cfg?: BulletConfig;
    public belongSkill?: Skill;

    constructor() {
        this._hurt = PropertyVo.Hurt.Pool.pop();
    }

    /**
     * Get/Set hurt property
     */
    get hurt(): PropertyVo.Hurt {
        return this._hurt;
    }

    set hurt(value: PropertyVo.Hurt) {
        this._hurt = value;
    }

    /**
     * Get/Set bullet ID
     */
    get bulletId(): number {
        return this._bulletId;
    }

    set bulletId(value: number) {
        this._bulletId = value;
        this.cfg = Cfg.BulletEffect.get(this.bulletId);
    }

    /**
     * Get bullet prefab path
     */
    get bulletPath(): string {
        if (this.bulletId) {
            return `entity/fight/Bullet/${this.cfg!.prefab}`;
        } else {
            return `entity/fight/Bullet/${this.belongSkill!.skillMainID}`;
        }
    }

    /**
     * Get hit audio ID
     */
    get hitAudioId(): number {
        return this.cfg!.soundhitId;
    }

    /**
     * Get/Set entity type that owns this bullet
     */
    get belong(): EntityType {
        return this._belong;
    }

    set belong(value: EntityType) {
        this._belong = value;
    }

    /**
     * Get skill configuration
     */
    get skillCfg(): SkillConfig | undefined {
        return this.belongSkill?.cutVo;
    }

    /**
     * Get bullet owner entity
     */
    get owner(): BaseEntity | undefined {
        return this.belongSkill?.getOwner();
    }

    /**
     * Get/Set camp type and update attack targets
     */
    get campType(): CampType {
        return this._campType;
    }

    set campType(value: CampType) {
        if (this.ignore) {
            this.ignore.length = 0;
        }
        
        this._campType = value;

        if (!this.skillCfg) {
            return;
        }

        // Set attack camps based on skill object type
        if (this.skillCfg.object === 3) {
            // Attack all camps
            this.atkCamp = [CampType.One, CampType.Two, CampType.Three];
            if (this.ignore && this.owner) {
                this.ignore.push(this.owner.ID);
            }
        } else if (this.skillCfg.object === 2) {
            // Attack same camp (friendly fire)
            this.atkCamp = [value];
        } else {
            // Attack enemy camp
            this.atkCamp = [value === CampType.One ? CampType.Two : CampType.One];
        }
    }

    /**
     * Set start position
     * @param pos - Starting position
     * @returns This instance for chaining
     */
    setStartPos(pos: cc.Vec2): this {
        this.startPos.x = pos.x;
        this.startPos.y = pos.y;
        return this;
    }

    /**
     * Set multiple attributes at once
     * @param attributes - Object containing attributes to set
     * @returns This instance for chaining
     */
    setAttribute(attributes: Partial<BulletVo>): this {
        for (const key in attributes) {
            if (this.hasOwnProperty(key)) {
                (this as any)[key] = attributes[key as keyof BulletVo];
            }
        }
        return this;
    }

    /**
     * Set target position
     * @param pos - Target position
     * @returns This instance for chaining
     */
    setTargetPos(pos: cc.Vec2): this {
        this.targetPos.x = pos.x;
        this.targetPos.y = pos.y;
        return this;
    }

    /**
     * Set shoot direction
     * @param dir - Shoot direction vector
     * @returns This instance for chaining
     */
    setShootDir(dir: cc.Vec2): this {
        this.shootDir.x = dir.x;
        this.shootDir.y = dir.y;
        return this;
    }

    /**
     * Check if bullet can hit target
     * @param target - Target entity
     * @returns True if can hit
     */
    canHit(target: BaseEntity): boolean {
        // Check if target is in ignore list
        if (this.ignore.includes(target.ID)) {
            return false;
        }

        // Check if target camp is in attack camps
        return this.atkCamp.includes(target.campType);
    }

    /**
     * Get remaining life time
     * @param currentTime - Current time
     * @returns Remaining life time
     */
    getRemainingLifeTime(currentTime: number): number {
        if (this.isForever) {
            return Infinity;
        }
        return Math.max(0, this.lifeTime - currentTime);
    }

    /**
     * Check if bullet is expired
     * @param currentTime - Current time
     * @returns True if expired
     */
    isExpired(currentTime: number): boolean {
        return !this.isForever && currentTime >= this.lifeTime;
    }

    /**
     * Reset bullet to default state (for object pooling)
     */
    unuse(): void {
        this.crossNum = 0;
        this.isHitBack = true;
        this.hitBack = 0;
        this.lotId = 0;
        this._bulletId = 0;
        this._belong = EntityType.Role;
        this.ignore.length = 0;
        this._campType = CampType.Not;
        this.atkCamp.length = 0;
        this.startPos.set(0, 0);
        this.targetPos.set(0, 0);
        this.shootDir.set(0, 0);
        this.lifeTime = 2;
        this.isForever = false;
        this.speed = 500;
        this.bulletType = 2;
        this.range = 1;
        this.effectType = 0;
        this.scale = 1;
        this.radius = null;
        this.size = 0;
        this.cfg = undefined;
        this.belongSkill = undefined;
    }

    /**
     * Initialize bullet when retrieved from pool
     */
    reuse(...args: any[]): void {
        // Initialize with provided arguments if any
        if (args.length > 0) {
            this.setAttribute(args[0]);
        }
    }

    /**
     * Destroy bullet and clean up resources
     */
    destroy(): void {
        if (this._hurt) {
            PropertyVo.Hurt.Pool.push(this._hurt);
        }
        this.unuse();
    }
}
