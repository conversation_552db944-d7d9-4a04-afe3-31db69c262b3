export enum ListenID {
    _Start = 999,
    Game_UpdateGold = 1000,
    Game_UpdatePower = 1001,
    Game_SecondDay = 1002,
    Game_StartLoadMap = 1003,
    Game_EndLoadMap = 1004,
    Game_Load = 1005,
    Game_Replay = 1006,
    Game_NextLV = 1007,
    Game_PauseTime = 1008,
    Cheat_ShowCheatInfo = 1009,
    Cheat_SetStageNum = 1010,
    Cheat_PassGuide = 1011,
    Net_InitSocketCallback = 1012,
    Platform_NavigateTo = 1013,
    Login_Start = 1014,
    Login_Finish = 1015,
    Login_SelectServer = 1016,
    Login_ConnectServer = 1017,
    Login_UpdateServerList = 1018,
    Login_SwitchServer = 1019,
    Login_OnConnectServer = 1020,
    Login_OpenSelectServerView = 1021,
    AgeCanView_Open = 1022,
    AgeCanView_Close = 1023,
    Net_OnReady = 1024,
    Net_OnDisconnect = 1025,
    Net_Disconnect = 1026,
    Net_Reconnect = 1027,
    Net_Error = 1028,
    Ad_ShowBanner = 1029,
    Ad_HideBanner = 1030,
    Ad_ShowVideo = 1031,
    Ad_ShowFullVideo = 1032,
    Ad_BannerCall = 1033,
    Ad_ShowInsertAd = 1034,
    Ad_ShowNativeAd = 1035,
    Ad_CheckShowNativeAd = 1036,
    Ad_HideNativeAd = 1037,
    Ad_ResetAutoFullAdTime = 1038,
    Ad_ShowAdDebuggView = 1039,
    Ad_SetAdCounting = 1040,
    Ad_CheatNoAd = 1041,
    Activity_OpenExchangeCode = 1042,
    Setting_EnableMusic = 1043,
    Setting_EnableAudio = 1044,
    Setting_ValueMusic = 1045,
    Setting_ValueAudio = 1046,
    Setting_EnableDub = 1047,
    Setting_EnableShake = 1048,
    Setting_SetSavingMode = 1049,
    Setting_PlayShake = 1050,
    Setting_OpenView = 1051,
    Setting_OpenUserAndPolicy = 1052,
    Setting_ShakeCamera = 1053,
    Setting_OpenGameService = 1054,
    Setting_SetClipboardData = 1055,
    Common_ShowFuncDesc = 1056,
    Common_ShowWeekAward = 1057,
    Common_GotoFunc = 1058,
    Common_ExChangeGoods = 1059,
    Common_OperateTime = 1060,
    Common_ShowDiamondNode = 1061,
    Common_ShowFlyReward = 1062,
    Conflate_DataUpdate = 1063,
    Conflate_TimeUpdate = 1064,
    Conflate_GameReset = 1065,
    Event_SendEvent = 1066,
    Event_SetSuperProperties = 1067,
    Event_SetUserProperty = 1068,
    Event_LoginTA = 1069,
    Event_LogOutTA = 1070,
    Main_OpenView = 1071,
    Main_SetMainViewVisible = 1072,
    Main_UpdateCurPage = 1073,
    Main_SetBtnEnable = 1074,
    Item_GoodsChange = 1075,
    Item_DiamondChange = 1076,
    Fight_onRoleMove = 1077,
    Fight_onRoleIdle = 1078,
    Fight_SetPause = 1079,
    Fight_BackToMain = 1080,
    Fight_ExpUpdate = 1081,
    Fight_LevelUp = 1082,
    Fight_UpdateGameTime = 1083,
    Fight_ShowEffect = 1084,
    Fight_GameRound = 1085,
    Fight_Kill = 1086,
    Fight_CountDown = 1087,
    Fight_GameMonsterCome = 1088,
    Fight_GameBossCome = 1089,
    Fight_SetTrack = 1090,
    Fight_AddSkill = 1091,
    Fight_AddBuff = 1092,
    Fight_OnBuff = 1093,
    Fight_OnBuffChange = 1094,
    Fight_OnSkillChange = 1095,
    Fight_OnGameState = 1096,
    Fight_onChangeRole = 1097,
    Fight_AddPet = 1098,
    Fight_SpawnHurt = 1099,
    Fight_BeHit = 1100,
    Fight_Dead = 1101,
    Fight_PetDead = 1102,
    Fight_MonsterHurt = 1103,
    Fight_OnHpRecover = 1104,
    Fight_MonsterEscape = 1105,
    Fight_End = 1106,
    Fight_Win = 1107,
    Fight_ReliveSuccess = 1108,
    Fight_OpenView = 1109,
    Fight_OpenPetTryView = 1110,
    Fight_OpenTowerView = 1111,
    Fight_OpenRoleTryView = 1112,
    Fight_OpenGameBitView = 1113,
    Fight_OpenSelectView = 1114,
    Fight_OpenFightUIView = 1115,
    Fight_OpenReliveView = 1116,
    Badge_Set = 1117,
    Badge_Update = 1118,
    Badge_Add = 1119,
    Item_ShowNormalTips = 1120,
    Refresh_Item = 1121,
    Refresh_List = 1122,
    BottomBar_OpenView = 1123,
    Hero_OpenView = 1124,
    Hero_SetViewVisible = 1125,
    Hero_PropChange = 1126,
    Test_OpenView = 1127,
    Guide_OpenView = 1128,
    Skill_OpenStoreView = 1129,
    Skill_OpenSelcectView = 1130,
    Skill_CloseSelcectView = 1131,
    Buff_OpenSelcectView = 1132,
    Buff_CloseSelcectView = 1133,
    HideBuffInfo = 1134,
    ReduceActiveSkill = 1135,
    ReducePassiveSkill = 1136,
    Tower_FightRoundTickUpdate = 1137,
    Tower_SpeedUp = 1138,
    Tower_FightRoundTickStart = 1139,
    Tower_ShowShop = 1140,
    CatGame_GetCoin = 1141,
    CatGame_BackToReady = 1142,
    BottomBar_SelectGame = 1143,
    Pop_ShowTInfo = 1144,
    Main_ResetView = 1145,
    Game_LoadFinish = 1146,
    CatGame_EnableMenu = 1147,
    CatGame_OnTick = 1148,
    CatGame_CheckWeapon = 1149,
    CatGame_WorkHandle = 1150,
    CatGame_FightEnd = 1151,
    CatGame_FightWin = 1152,
    Main_LoadAnim = 1153,
    CatGame_CleanSkill = 1154,
    Main_HandleBox = 1155,
    Fight_OpDialog = 1156,
    CatGameShowAward = 1157,
    CatGame_QuickToHome = 1158,
    Fight_TeammatesChanges = 1159,
    CatGame_GetCoin_Onready = 1160,
    Fight_EntityUpdate = 1161,
    CatGame_GuideStart = 1162,
    CatGame_GuideUpgrade = 1163,
    CatGame_ShowWorkBox = 1164,
    CatGame_StarGame = 1165,
    CatGame_ReadyCall = 1166,
    M3_CloseDlsView = 1167,
    M3_ShowDlsFailView = 1168,
    M3_ShowDlsWinView = 1169,
    CatGame_StartDls = 1170,
    M3_Dls_SetTimer = 1171,
    M3_Dls_SetSkillTips = 1172,
    Fight_WeatherSwitch = 1173,
    M3_CallStart = 1174,
    ScreenMatch_Finish = 1175,
    ModeHide_SetHideBtn = 1176,
    ModeHide_RoleHide = 1177,
    Fight_GameRoundType = 1178,
    M3_AddTempCoin = 1179,
    M3_2menuUnlock = 1180,
    M3_ShowMenu = 1181,
    M3_HideMenu = 1182,
    M3_ShowMenuView = 1183,
    M3_1MenuHandle = 1184,
    M3_2MenuHandle = 1185,
    M3_2menuCheck = 1186,
    M3_Update2Menu = 1187,
    Tower_FightRoundTickStart1 = 1188,
    M3_HideMenuView = 1189,
    Fight_RoundState = 1190,
    M3_ShowGetAmethyst = 1191,
    M3_CheckFreeSkill = 1192,
    M3_CheckHighSkillUnlock = 1193,
    knasChange = 1194,
    ModeBack_PackState = 1195,
    M20_CheckMenu = 1196,
    M20_RoungChange = 1197,
    M20_ShowEquipInfo = 1198,
    M20_EquipUpgrade = 1199,
    M20_CheckPackData = 1200,
    ResetView = 1201,
    Fight_ClickBackHome = 1202,
    Common_Guide_Forcus = 1203,
    Common_Guide_Anim = 1204,
    Common_Guide_Close = 1205,
    Ks_SetGame = 1206,
    Fight_EntityUseSkillEnd = 1207,
    Fight_HandleButton = 1208,
    Common_Guide_OnlyDesc = 1209,
    M20_GetRoleFrag = 1210,
    M20_RoleChange = 1211,
    M20_ShowRoleInfo = 1212,
    M20_RoleListChange = 1213,
    M20_CheckEnergy = 1214,
    Platform_AddtoDestop = 1215,
    M20_GuideHandle = 1216,
    Fight_GuideChange = 1217,
    M20_UpdateBoxExp = 1218,
    Pay_ToPay = 1219,
    Pay_PayRewardProductId = 1220,
    Pay_LoadingView = 1221,
    Shop_InfoUpDate = 1222,
    Shop_BuyCharge = 1223,
    Shop_ShopItemList = 1224,
    ModeBack_RoundUnlock = 1225,
    Shop_RecoveryBuy = 1226,
    Fight_OnSkill = 1227,
    Task_OpenMainView = 1228,
    Task_UpdateProgress = 1229,
    Task_GetResult = 1230,
    Task_Jump = 1231,
    Task_UpdateAchieveDate = 1232,
    Task_ClearData = 1233,
    Task_UpdateNextDayView = 1234,
    JumpOpenMainView = 1235,
    FightHightRound = 1236,
    Fight_Vampirism = 1237,
    Fight_ShowGameTips = 1238,
    Fight_PrePare_Refresh_SweepView = 1239,
    Item_GetReward = 1240,
    Fight_PackView_Full_Block = 1241,
    PrePare_Fight_Skip = 1242,
    M34_GetBuff = 1243,
    M31_Update_Bullet_Num = 1244,
    M31_Update_Hand_Style = 1245,
    MoreGame_Refresh = 1246,
    Is_Back_From_Try_Play = 1247,
    M36_Launcher_Anim = 1248,
    M36_Update_Progress = 1249,
    NO_BG_Moving = 1250,
    M37_DragonWarMove = 1251,
    M37_DragonWarPowerChange = 1252,
    M37_Wall_Enter = 1253,
    M37_DragonCollide = 1254,
    Platform_ShowTestAD = 1255,
    ByteDance_Check_Gift = 1256,
    Platform_CheckScene = 1257
}
