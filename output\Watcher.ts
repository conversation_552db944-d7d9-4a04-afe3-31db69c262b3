import BaseEntity from "./BaseEntity";
import { Log } from "./Log";

/**
 * Watcher class for delayed function execution
 * Provides timer functionality with repeat capabilities
 */
export class Watcher {
    private _id: number = 0;
    private _nextTime: number = 0;
    private _delay: number = 0;
    private _times: number = 0;
    private _func: Function | null = null;
    private _args: any = null;
    private _target: any = null;
    
    public tag: any = undefined;

    /**
     * Get watcher ID
     */
    get id(): number {
        return this._id;
    }

    /**
     * Get next execution time
     */
    get nextTime(): number {
        return this._nextTime;
    }

    /**
     * Get/Set delay between executions
     */
    get delay(): number {
        return this._delay;
    }

    set delay(value: number) {
        this._delay = value;
    }

    /**
     * Get/Set remaining execution times
     */
    get times(): number {
        return this._times;
    }

    set times(value: number) {
        this._times = value;
    }

    /**
     * Get function arguments
     */
    get args(): any {
        return this._args;
    }

    /**
     * Check if watcher is enabled and should execute
     */
    get enable(): boolean {
        // Check if target is a BaseEntity and if it's active
        const isTargetActive = !(this._target instanceof BaseEntity && !this._target.isActive);
        
        // Check if we have remaining executions or infinite executions (-1)
        const hasExecutions = this._times > 0 || this._times === -1;
        
        return isTargetActive && hasExecutions;
    }

    /**
     * Set tag for identification
     * @param tag - Tag value
     * @returns This watcher for chaining
     */
    setTag(tag: any): this {
        this.tag = tag;
        return this;
    }

    /**
     * Set watcher ID (internal use)
     * @param id - Watcher ID
     */
    _setId(id: number): void {
        this._id = id;
    }

    /**
     * Initialize watcher with callback
     * @param nextTime - Next execution time
     * @param delay - Delay between executions
     * @param func - Function to execute
     * @param args - Arguments for function
     * @param target - Target object for function context
     * @param times - Number of times to execute (default: 1, -1 for infinite)
     */
    initWithCallback(
        nextTime: number,
        delay: number,
        func: Function,
        args: any = null,
        target: any = null,
        times: number = 1
    ): void {
        if (times < 0) {
            times = -1; // Normalize negative values to -1 for infinite
        }

        this._nextTime = nextTime;
        this._delay = delay;
        this._times = times;
        this._func = func;
        this._args = args;
        this._target = target;
        this.tag = undefined;
    }

    /**
     * Cancel the watcher
     * @param executeOnce - Whether to execute the function once before canceling
     */
    cancel(executeOnce: boolean = false): void {
        if (executeOnce) {
            this._callBack();
        }

        this._times = 0;
        this._func = null;
        this._args = null;
        this._target = null;
        this.tag = undefined;
    }

    /**
     * Execute the callback function (internal use)
     */
    _callBack(): void {
        if (!this.enable) {
            return;
        }

        // Update execution count and next time
        if (this._times > 0) {
            this._times--;
            this._nextTime = this.nextTime + this.delay;
        } else if (this._times === -1) {
            // Infinite executions
            this._nextTime = this.nextTime + this.delay;
        } else {
            Log.error("Watcher._CallBack times error:", this._times);
            return;
        }

        // Execute the function
        if (this._func != null) {
            try {
                this._func.call(this._target, this._args);
            } catch (error) {
                Log.error("Watcher callback execution error:", error);
            }
        }
    }

    /**
     * Check if watcher is finished (no more executions)
     */
    get isFinished(): boolean {
        return this._times === 0;
    }

    /**
     * Check if watcher has infinite executions
     */
    get isInfinite(): boolean {
        return this._times === -1;
    }

    /**
     * Get remaining time until next execution
     * @param currentTime - Current time
     * @returns Remaining time
     */
    getRemainingTime(currentTime: number): number {
        return Math.max(0, this._nextTime - currentTime);
    }

    /**
     * Reset watcher to initial state
     * @param times - New execution count
     */
    reset(times?: number): void {
        if (times !== undefined) {
            this._times = times < 0 ? -1 : times;
        }
        // Reset next time based on current delay
        this._nextTime = Date.now() + this._delay;
    }

    /**
     * Pause the watcher by setting times to 0 temporarily
     */
    pause(): void {
        this._times = 0;
    }

    /**
     * Resume the watcher with specified times
     * @param times - Number of executions to resume with
     */
    resume(times: number = 1): void {
        this._times = times < 0 ? -1 : times;
    }

    /**
     * Get string representation for debugging
     */
    toString(): string {
        let result = `[Watcher] index:${this.id} time:${this.nextTime} times:${this.times}`;
        
        if (this._args == null) {
            result += ` func:${this._func}`;
        } else {
            result += ` argfunc:${this._func} ${this._args}`;
        }
        
        if (this.tag !== undefined) {
            result += ` tag:${this.tag}`;
        }
        
        return result;
    }

    /**
     * Create a simple watcher for one-time execution
     * @param delay - Delay before execution
     * @param func - Function to execute
     * @param target - Target object
     * @param args - Function arguments
     * @returns New watcher instance
     */
    static createSimple(delay: number, func: Function, target?: any, args?: any): Watcher {
        const watcher = new Watcher();
        const nextTime = Date.now() + delay * 1000; // Convert to milliseconds
        watcher.initWithCallback(nextTime, delay, func, args, target, 1);
        return watcher;
    }

    /**
     * Create a repeating watcher
     * @param delay - Delay between executions
     * @param func - Function to execute
     * @param times - Number of times to execute (-1 for infinite)
     * @param target - Target object
     * @param args - Function arguments
     * @returns New watcher instance
     */
    static createRepeating(delay: number, func: Function, times: number = -1, target?: any, args?: any): Watcher {
        const watcher = new Watcher();
        const nextTime = Date.now() + delay * 1000; // Convert to milliseconds
        watcher.initWithCallback(nextTime, delay, func, args, target, times);
        return watcher;
    }
}
