/**
 * Geometric intersection detection utilities
 */

// Temporary vectors for calculations
const tempVec1 = cc.v2();
const tempVec2 = cc.v2();
const tempVec3 = cc.v2();
const tempVec4 = cc.v2();

/**
 * Circle interface for intersection tests
 */
export interface Circle {
    worldPosition: cc.Vec2;
    worldRadius: number;
}

/**
 * Range interface for 1D overlap tests
 */
export interface Range {
    min: number;
    max: number;
}

/**
 * Check if two ranges overlap
 */
function rangeOverlap(range1: Range, range2: Range): boolean {
    const min = range1.min < range2.min ? range1.min : range2.min;
    const max = range1.max > range2.max ? range1.max : range2.max;
    return range1.max - range1.min + (range2.max - range2.min) < max - min;
}

/**
 * Intersection detection utilities
 */
export class Intersection {
    /**
     * Test if a polygon intersects with a circle
     */
    static polygonCircle(polygon: cc.Vec2[], circle: Circle): boolean {
        const center = circle.worldPosition;
        
        // Check if circle center is inside polygon
        if (this.pointInPolygon(center, polygon)) {
            return true;
        }

        const radiusSqr = circle.worldRadius * circle.worldRadius;
        
        // Check distance to each edge
        for (let i = 0; i < polygon.length; i++) {
            const p1 = i === 0 ? polygon[polygon.length - 1] : polygon[i - 1];
            const p2 = polygon[i];
            
            if (this.pointLineDistanceSqr(center, p1, p2, true) < radiusSqr) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Calculate squared distance from point to line segment
     */
    static pointLineDistanceSqr(point: cc.Vec2, lineStart: cc.Vec2, lineEnd: cc.Vec2, clamp: boolean = false): number {
        const dx = lineEnd.x - lineStart.x;
        const dy = lineEnd.y - lineStart.y;
        const lengthSqr = dx * dx + dy * dy;
        
        let t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / lengthSqr;
        
        let closestPoint: cc.Vec2;
        if (clamp) {
            if (lengthSqr) {
                if (t < 0) {
                    closestPoint = lineStart;
                } else if (t > 1) {
                    closestPoint = lineEnd;
                } else {
                    cc.Vec2.set(tempVec1, lineStart.x + t * dx, lineStart.y + t * dy);
                    closestPoint = tempVec1;
                }
            } else {
                closestPoint = lineStart;
            }
        } else {
            cc.Vec2.set(tempVec1, lineStart.x + t * dx, lineStart.y + t * dy);
            closestPoint = tempVec1;
        }

        const distX = point.x - closestPoint.x;
        const distY = point.y - closestPoint.y;
        return distX * distX + distY * distY;
    }

    /**
     * Test if two circles intersect
     */
    static circleCircle(circle1: Circle, circle2: Circle): boolean {
        cc.Vec2.subtract(tempVec4, circle1.worldPosition, circle2.worldPosition);
        const distanceSqr = tempVec4.magSqr();
        const radiusSum = circle1.worldRadius + circle2.worldRadius;
        return distanceSqr < radiusSum * radiusSum;
    }

    /**
     * Test if two line segments intersect
     */
    static lineLine(line1Start: cc.Vec2, line1End: cc.Vec2, line2Start: cc.Vec2, line2End: cc.Vec2): boolean {
        const d1 = (line2End.x - line2Start.x) * (line1Start.y - line2Start.y) - (line2End.y - line2Start.y) * (line1Start.x - line2Start.x);
        const d2 = (line1End.x - line1Start.x) * (line1Start.y - line2Start.y) - (line1End.y - line1Start.y) * (line1Start.x - line2Start.x);
        const d3 = (line2End.y - line2Start.y) * (line1End.x - line1Start.x) - (line2End.x - line2Start.x) * (line1End.y - line1Start.y);

        if (d3 === 0) return false; // Lines are parallel

        const t1 = d1 / d3;
        const t2 = d2 / d3;

        return t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1;
    }

    /**
     * Test if a point is inside a polygon using ray casting
     */
    static pointInPolygon(point: cc.Vec2, polygon: cc.Vec2[]): boolean {
        let inside = false;
        const x = point.x;
        const y = point.y;

        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
            const xi = polygon[i].x;
            const yi = polygon[i].y;
            const xj = polygon[j].x;
            const yj = polygon[j].y;

            if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside;
            }
        }

        return inside;
    }

    /**
     * Test if a rectangle intersects with a circle
     */
    static rectCircle(rect: cc.Rect, circle: Circle): boolean {
        const center = circle.worldPosition;
        const radius = circle.worldRadius;

        // Find closest point on rectangle to circle center
        const closestX = Math.max(rect.xMin, Math.min(center.x, rect.xMax));
        const closestY = Math.max(rect.yMin, Math.min(center.y, rect.yMax));

        // Calculate distance from circle center to closest point
        const distanceX = center.x - closestX;
        const distanceY = center.y - closestY;
        const distanceSqr = distanceX * distanceX + distanceY * distanceY;

        return distanceSqr < radius * radius;
    }

    /**
     * Test if two rectangles intersect
     */
    static rectRect(rect1: cc.Rect, rect2: cc.Rect): boolean {
        return !(rect1.xMax < rect2.xMin || 
                rect2.xMax < rect1.xMin || 
                rect1.yMax < rect2.yMin || 
                rect2.yMax < rect1.yMin);
    }

    /**
     * Test if a point is inside a rectangle
     */
    static pointInRect(point: cc.Vec2, rect: cc.Rect): boolean {
        return point.x >= rect.xMin && 
               point.x <= rect.xMax && 
               point.y >= rect.yMin && 
               point.y <= rect.yMax;
    }

    /**
     * Test if a point is inside a circle
     */
    static pointInCircle(point: cc.Vec2, circle: Circle): boolean {
        const dx = point.x - circle.worldPosition.x;
        const dy = point.y - circle.worldPosition.y;
        const distanceSqr = dx * dx + dy * dy;
        return distanceSqr <= circle.worldRadius * circle.worldRadius;
    }

    /**
     * Get intersection point of two lines (infinite lines, not segments)
     */
    static lineIntersection(line1Start: cc.Vec2, line1End: cc.Vec2, line2Start: cc.Vec2, line2End: cc.Vec2): cc.Vec2 | null {
        const x1 = line1Start.x, y1 = line1Start.y;
        const x2 = line1End.x, y2 = line1End.y;
        const x3 = line2Start.x, y3 = line2Start.y;
        const x4 = line2End.x, y4 = line2End.y;

        const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
        if (Math.abs(denom) < 1e-10) return null; // Lines are parallel

        const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
        
        return cc.v2(
            x1 + t * (x2 - x1),
            y1 + t * (y2 - y1)
        );
    }
}

/**
 * Angle interpolation utilities
 */
export class AngleSlerp {
    /**
     * Spherical linear interpolation for angles
     */
    static slerp(from: number, to: number, t: number): number {
        // Normalize angles to [-π, π]
        from = this.normalizeAngle(from);
        to = this.normalizeAngle(to);

        // Find the shortest path
        let diff = to - from;
        if (diff > Math.PI) {
            diff -= 2 * Math.PI;
        } else if (diff < -Math.PI) {
            diff += 2 * Math.PI;
        }

        return from + diff * t;
    }

    /**
     * Normalize angle to [-π, π] range
     */
    static normalizeAngle(angle: number): number {
        while (angle > Math.PI) angle -= 2 * Math.PI;
        while (angle < -Math.PI) angle += 2 * Math.PI;
        return angle;
    }

    /**
     * Get the shortest angular distance between two angles
     */
    static angleDifference(from: number, to: number): number {
        let diff = to - from;
        while (diff > Math.PI) diff -= 2 * Math.PI;
        while (diff < -Math.PI) diff += 2 * Math.PI;
        return diff;
    }

    /**
     * Linear interpolation for angles (considering wrap-around)
     */
    static lerp(from: number, to: number, t: number): number {
        return from + this.angleDifference(from, to) * t;
    }
}
