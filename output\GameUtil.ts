import { LanguageFun } from "./LanguageFun";
import { Time } from "./Time";
import Md5 from "./Md5";
import * as lzstring from "./lzstring";

// Type aliases for Cocos Creator types
export const MyVec3 = cc.Vec3;
export const MyVec2 = cc.Vec2;
export const MyMat4 = cc.Mat4;
export const MyRect = cc.Rect;

// Buffer cache for dynamic class loading
export const bufferCache: { [key: string]: any } = {};

export function cacheFunction(name: string) {
    return function(target: any) {
        bufferCache[name] = target;
    };
}

export function getClassByName(name: string): any {
    return bufferCache[name];
}

// Time formatting interface
interface TimeFormat {
    str: string;
    s: string;
    m: string;
    h: string;
    result?: string;
}

// Date format interface
interface DateFormat {
    year: number;
    monthStr: string;
    dateStr: string;
    currentdate: string;
}

// Weight item interface
interface WeightItem {
    id: any;
    w: number;
    val?: any[];
}

// Weight selection context
interface WeightContext {
    totalW: number;
    selected: any[];
    seen: Set<any>;
}

export class GameUtil {
    static offsetSize: cc.Vec2 = cc.Vec2.ZERO;
    static getDesignSize: cc.Size = cc.Size.ZERO;

    /**
     * Get log time description in Chinese
     */
    static getLogTimeDesc(seconds: number): string {
        if (seconds < 60) {
            return cc.js.formatStr("%d秒前", seconds);
        } else if (seconds < 3600) {
            return cc.js.formatStr("%d分钟前", Math.floor(seconds / 60));
        } else if (seconds < 86400) {
            return cc.js.formatStr("%d小时前", Math.floor(seconds / 3600));
        } else {
            return cc.js.formatStr("%d天前", Math.floor(seconds / 3600 / 24));
        }
    }

    /**
     * Set click listener for a node
     */
    static setListener(node: cc.Node, callback: Function, target?: any): void {
        if (callback != null && cc.isValid(node)) {
            node.on("click", callback, target);
        }
    }

    /**
     * Format date to string
     */
    static dateFormat(timestamp: number): string {
        const date = new Date();
        date.setTime(timestamp);
        const year = date.getFullYear();
        let month: string | number = date.getMonth();
        let day: string | number = date.getDate();
        
        ++month;
        if (month < 10) month = "0" + month;
        if (day < 10) day = "0" + day;
        
        return year + "/" + month + "/" + day;
    }

    /**
     * Get formatted current date
     */
    static getNowFormatDate(timestamp?: number | null): DateFormat {
        if (timestamp && (timestamp + "").length === 10) {
            timestamp *= 1000;
        }
        
        const date = new Date(timestamp || Time.serverTimeMs);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        
        const monthStr = month < 10 ? "0" + month : month.toString();
        const dateStr = day < 10 ? "0" + day : day.toString();
        
        return {
            year: year,
            monthStr: monthStr,
            dateStr: dateStr,
            currentdate: year + "-" + monthStr + "-" + dateStr
        };
    }

    /**
     * Format seconds to time string
     */
    static formatSeconds(totalSeconds: number): TimeFormat {
        if (totalSeconds < 0 || !totalSeconds) {
            return {
                str: "00:00:00",
                s: "00",
                m: "00",
                h: "00"
            };
        }

        let seconds = parseInt(totalSeconds.toString());
        let minutes = 0;
        let hours = 0;

        if (seconds > 60) {
            minutes = seconds / 60;
            seconds %= 60;
            if (minutes > 60) {
                hours = minutes / 60;
                minutes %= 60;
            }
        }

        hours = Math.floor(hours);
        minutes = Math.floor(minutes);
        seconds = Math.floor(seconds);

        let result = seconds + "秒";
        if (minutes > 0) result = minutes + "分" + result;
        if (hours > 0) result = hours + "小时" + result;

        const formatted = {
            s: seconds < 10 ? "0" + seconds : seconds.toString(),
            m: minutes < 10 ? "0" + minutes : minutes.toString(),
            h: hours < 10 ? "0" + hours : hours.toString()
        };

        return {
            s: formatted.s,
            m: formatted.m,
            h: formatted.h,
            str: formatted.h + ":" + formatted.m + ":" + formatted.s,
            result: result
        };
    }

    /**
     * Convert angle to radians
     */
    static AngleToRadinas(angle: number): number {
        return angle * (Math.PI / 180);
    }

    /**
     * Get angle between two points
     */
    static GetAngle(point1: cc.Vec2, point2: cc.Vec2 = cc.Vec2.ZERO): number {
        return Math.round(Math.atan2(point1.y - point2.y, point1.x - point2.x) * (180 / Math.PI) + 360) % 360;
    }

    /**
     * Get direction vector from angle
     */
    static GeAngletDir(angle: number): cc.Vec2 {
        const radians = angle * Math.PI / 180;
        return cc.v2(Math.cos(radians), Math.sin(radians));
    }

    /**
     * Convert radians to angle
     */
    static RadinasToAngle(radians: number): number {
        return 180 * radians / Math.PI;
    }

    /**
     * Get position from angle and length
     */
    static AngleAndLenToPos(angle: number, length: number = 1): cc.Vec2 {
        const tempVec = cc.v2();
        cc.Vec2.UP.rotate(cc.misc.degreesToRadians(angle), tempVec);
        cc.Vec2.multiplyScalar(tempVec, tempVec, length);
        return tempVec;
    }

    /**
     * Get distance between two points
     */
    static getDistance(point1: cc.Vec2, point2: cc.Vec2): number {
        return Math.sqrt(Math.pow(point1.x - point2.x, 2) + Math.pow(point1.y - point2.y, 2));
    }

    /**
     * Get squared distance between two points (faster than getDistance)
     */
    static getDistanceSqrt(point1: cc.Vec2, point2: cc.Vec2): number {
        return Math.pow(point1.x - point2.x, 2) + Math.pow(point1.y - point2.y, 2);
    }

    /**
     * Generate random integer between min and max (exclusive)
     */
    static random(min: number, max: number): number {
        return min + Math.floor(Math.random() * (max - min));
    }

    /**
     * Check if random weight passes
     */
    static weight(probability: number): boolean {
        if (probability < 1) probability *= 100;
        return this.random(0, 100) < probability;
    }

    /**
     * Check if random float weight passes
     */
    static weightFloat(probability: number): boolean {
        return this.random(0, 100) < 100 * probability;
    }

    /**
     * Get weighted random value from array
     */
    static weightGetValue<T>(items: T[], weightKey: string = "w", multiplier: number = 1): T | null {
        if (!items) return null;

        let totalWeight = 0;
        const validItems: T[] = [];

        for (let i = 0; i < items.length; i++) {
            if ((items[i] as any)[weightKey] !== 0) {
                totalWeight += (items[i] as any)[weightKey];
                validItems.push(items[i]);
            }
        }

        const randomValue = this.random(0, totalWeight);
        let currentWeight = 0;

        for (let i = 0; i < validItems.length; i++) {
            const itemWeight = (validItems[i] as any)[weightKey] * multiplier;
            if (randomValue <= currentWeight + itemWeight) {
                return validItems[i];
            }
            currentWeight += itemWeight;
        }

        return validItems[0];
    }

    /**
     * Convert array to weight format
     */
    static arrToWeight(arr: [any, number][]): WeightItem[] {
        const result: WeightItem[] = [];
        arr.forEach(item => {
            const weightItem: WeightItem = {
                id: item[0],
                w: item[1]
            };
            result.push(weightItem);
        });
        return result;
    }

    /**
     * Get weighted random list
     */
    static weightGetList<T extends WeightItem>(
        items: T[],
        count: number,
        weightKey: string = "w",
        callback?: (item: T, context: WeightContext) => void
    ): T[] {
        if (items.length < count) {
            return items.filter(item => (item as any)[weightKey] > 0);
        }

        const context: WeightContext = {
            totalW: items.reduce((sum, item) => sum + Number((item as any)[weightKey]), 0),
            selected: [],
            seen: new Set()
        };

        if (!context.totalW || typeof context.totalW !== "number") {
            cc.error("权重列表出错:", context, items);
            return [];
        }

        while (context.selected.length < count) {
            for (const item of items) {
                if (callback) callback(item, context);

                if (this.random(0, context.totalW) < Number((item as any)[weightKey]) &&
                    !context.seen.has(item.id)) {
                    context.selected.push(item);
                    context.seen.add(item.id);
                    break;
                }
            }
        }

        return context.selected;
    }

    /**
     * Get seconds until next day
     */
    static secondsUntilNextDay(): number {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth();
        const day = now.getDate();
        const nextDay = new Date(year, month, day + 1).getTime() - now.getTime();
        return Math.floor(nextDay / 1000);
    }

    /**
     * Check if two arrays have intersection
     */
    static hasIntersection<T>(arr1: T[], arr2: T[]): boolean {
        return arr1.reduce((hasIntersect, item) => hasIntersect || arr2.includes(item), false);
    }

    /**
     * Get intersection size of two arrays
     */
    static intersectionSize<T>(arr1: T[], arr2: T[]): number {
        const set2 = new Set(arr2);
        return arr1.filter(item => set2.has(item)).length;
    }

    /**
     * Get random index based on weights
     */
    static getRandomIndex(weights: number[]): number {
        const cumulativeWeights = weights.reduce((acc, weight) => {
            const lastWeight = acc.length > 0 ? acc[acc.length - 1] : 0;
            acc.push(lastWeight + weight);
            return acc;
        }, [] as number[]);

        const totalWeight = cumulativeWeights[cumulativeWeights.length - 1];
        const randomValue = Math.random() * totalWeight;

        return cumulativeWeights.findIndex(weight => randomValue < weight);
    }

    /**
     * Alternative random index method
     */
    static getRandomIndex2(weights: number[]): number {
        let totalWeight = 0;
        weights.forEach(weight => totalWeight += weight);

        const expandedArray: number[] = [];
        let currentIndex = 0;

        for (let i = 0; i < totalWeight; i++) {
            expandedArray.push(weights[currentIndex]);
            if (currentIndex === i) currentIndex++;
        }

        const randomIndex = this.random(0, totalWeight);
        return weights.indexOf(expandedArray[randomIndex]);
    }

    /**
     * Format number to abbreviated string
     */
    static changeNumStr(num: number, maxLength: number = 5, precision: number = 1): string {
        if (num.toString().length <= maxLength) {
            return num + "";
        }

        const suffixes = [" ", "K", "M", "B", "T", "q", "Q", "s", "S", "O"];
        let suffix: string | undefined;

        while ((suffix = suffixes.shift()) && num > 1000) {
            num /= 1000;
        }

        let result = "";
        if (suffix === " ") {
            result = num + suffix;
        } else {
            if (suffix === "K") precision = 0;
            result = num.toFixed(precision) + suffix;
        }

        return result;
    }

    /**
     * Split number with thousands separator
     */
    static splitThousands(num: number | string, precision: number = 0): string {
        if (typeof num !== "number") num = parseFloat(num as string);
        precision = precision == null ? 2 : precision;

        const parts = num.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+$)/g, ",");
        parts[1] = parts[1] ? parts[1].substr(0, precision) : "00000000000000000".substr(0, precision);

        if (precision) {
            return parts.join(".");
        } else {
            return parts[0];
        }
    }

    /**
     * Convert seconds to clock format
     */
    static changeSecondToClock(seconds: number, showZeroHours: boolean = false): string {
        const hours = parseInt((seconds / 3600).toString());
        const minutes = parseInt((seconds % 3600 / 60).toString());
        const secs = parseInt((seconds % 60).toString());

        let result = "";
        if (hours === 0) {
            if (showZeroHours) result += "00:";
        } else if (hours > 0 && hours < 10) {
            result += "0" + hours + ":";
        } else {
            result += hours + ":";
        }

        result += minutes === 0 ? "00" : (minutes > 0 && minutes < 10 ? "0" + minutes : minutes);
        result += ":";
        result += secs === 0 ? "00" : (secs > 0 && secs < 10 ? "0" + secs : secs);

        return result;
    }

    /**
     * Convert node position to another node's coordinate system
     */
    static convertNodePostionToOther(sourceNode: cc.Node, targetNode: cc.Node): cc.Vec3 {
        if (!sourceNode.parent) {
            return MyVec3.ZERO;
        }
        const worldPos = sourceNode.parent.convertToWorldSpaceAR(sourceNode.position);
        return targetNode.convertToNodeSpaceAR(worldPos);
    }

    /**
     * Get random array with different values
     */
    static getRandomListDiffArray(min: number, max: number): number[] {
        const result: number[] = [];
        const length = max - min + 1;

        for (let i = 0; i < length; i++) {
            result.push(i + min);
        }

        for (let i = max - min; i > 0; i--) {
            const randomIndex = GameUtil.random(0, i);
            [result[i], result[randomIndex]] = [result[randomIndex], result[i]];
        }

        return result;
    }

    /**
     * Get or add component to node
     */
    static getComponent<T extends cc.Component>(node: cc.Node, componentType: new() => T): T {
        let component = node.getComponent(componentType);
        if (!component) {
            component = node.addComponent(componentType);
        }
        return component;
    }

    /**
     * Add leading zeros to number
     */
    static prefixZero(num: number, length: number): string {
        return (Array(length).join("0") + num).slice(-length);
    }

    /**
     * Create event handler
     */
    static createHandler(target: cc.Node, handlerName: string, component: string): cc.Component.EventHandler {
        const handler = new cc.Component.EventHandler();
        handler.handler = handlerName;
        handler.target = target;
        handler.component = component;
        return handler;
    }

    /**
     * Deep copy with circular reference handling
     */
    static deepCopy<T>(obj: T, visited: Array<{original: any, copy: any}> = []): T {
        if (obj === null || typeof obj !== "object") {
            return obj;
        }

        const existingCopy = visited.filter(item => item.original === obj)[0];
        if (existingCopy) {
            return existingCopy.copy;
        }

        const copy = Array.isArray(obj) ? [] : {} as any;
        visited.push({ original: obj, copy: copy });

        Object.keys(obj).forEach(key => {
            copy[key] = GameUtil.deepCopy((obj as any)[key], visited);
        });

        return copy;
    }

    /**
     * Decompress string using LZ-string
     */
    static deCompressByLzstring(compressed: string): string {
        return lzstring.decompressFromBase64(compressed);
    }

    /**
     * Compress string using LZ-string
     */
    static compressByLzstring(data: string): string {
        return lzstring.compressToBase64(data);
    }

    /**
     * Get battle power calculation
     */
    static getBattlePower(stats: {
        attack: number;
        critPer: number;
        critRatio: number;
        hitRatio: number;
        ctrlRatio: number;
        hp: number;
        resitCrit: number;
        missRatio: number;
        resitCtrl: number;
    }): number {
        return Math.ceil((16 * stats.attack *
            (1 + stats.critPer / 1000 * 0.4) *
            (1 + stats.critRatio / 10000 * 0.2) *
            (1 + stats.hitRatio / 10000 * 0.2) *
            (1 + stats.ctrlRatio / 10000 * 0.2) +
            stats.hp *
            (1 + stats.resitCrit / 1000 * 0.4) *
            (1 + stats.missRatio / 10000 * 0.1) *
            (1 + stats.resitCtrl / 10000 * 0.2)) / 100);
    }
}

// CCTool namespace for additional utilities
export namespace CCTool {
    export class Language extends LanguageFun {
    }

    export class Material {
        static setGray(node: cc.Node): void {
            node.getComponentInChildren(cc.Sprite);
        }
    }

    export class System {
        static GetShaderScale(): number {
            if ((window as any).wonderSdk?.isNative) {
                return 10;
            } else {
                return 1;
            }
        }
    }

    export class CDManage {
        private limt: { [key: string]: number } = { default: 1 };
        private arr: { [key: string]: number } = {};
        private UpCDFun: { [key: string]: () => number } = {};

        constructor(limits?: { [key: string]: number }) {
            if (limits) {
                for (const key in limits) {
                    this.limt[key] = limits[key];
                }
                this.Init();
            }
        }

        Up(key: string = "default"): number {
            if (this.UpCDFun[key]) {
                this.limt[key] = this.UpCDFun[key]();
            }
            return this.limt[key];
        }

        Check(key: string = "default"): boolean {
            return !this.arr[key] || this.arr[key] <= 0;
        }

        CheckStamp(key: string = "default"): boolean {
            const currentTime = Time.serverTimeMs / 1000;
            if (currentTime - (this.arr[key] || 0) > this.limt[key]) {
                this.arr[key] = currentTime;
                return true;
            }
            return false;
        }

        CheckAndSet(key: string = "default"): boolean {
            if (this.Check(key)) {
                this.Set(key);
                return true;
            }
            return false;
        }

        Set(key: string = "default", value?: number): void {
            this.arr[key] = value !== undefined ? value : this.limt[key];
        }

        Clear(key: string = "default"): void {
            this.arr[key] = 0;
        }

        Init(): void {
            for (const key in this.UpCDFun) {
                this.limt[key] = 1;
            }
            for (const key in this.limt) {
                this.Up(key);
            }
        }

        OnUpdate(deltaTime: number): void {
            for (const key in this.arr) {
                this.arr[key] -= deltaTime;
            }
        }

        Reset(): void {
            for (const key in this.arr) {
                this.arr[key] = this.limt[key];
            }
        }
    }

    export class UI {
        private static ClickID: string;

        static AddClick(node: cc.Node, callback: (node: cc.Node) => void): void {
            node.off(cc.Node.EventType.TOUCH_START);
            node.off(cc.Node.EventType.TOUCH_END);

            node.on(cc.Node.EventType.TOUCH_START, () => {
                this.ClickID = node.uuid;
            });

            node.on(cc.Node.EventType.TOUCH_END, () => {
                if (this.ClickID === node.uuid) {
                    callback(node);
                }
            });
        }

        static CreateAnimationClip(name: string, spriteFrames: cc.SpriteFrame[], fps: number = 20): cc.AnimationClip {
            const clip = cc.AnimationClip.createWithSpriteFrames(spriteFrames, fps);
            clip.wrapMode = cc.WrapMode.Loop;
            clip.speed = 1;
            clip.name = name;
            return clip;
        }
    }
}
