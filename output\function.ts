import { Api } from "./Api";
import { md5 } from "./md51";
import { API_SECRET } from "./config";

/**
 * Response interface for API responses
 */
export interface ApiResponse<T = any> {
    code: number;
    msg: string;
    data: T;
}

/**
 * Sort object keys alphabetically and return new object
 * @param obj - Object to sort
 * @returns New object with sorted keys
 */
export function arrSort<T extends Record<string, any>>(obj: T): T {
    const sorted = {} as T;
    Object.keys(obj).sort().forEach(key => {
        sorted[key as keyof T] = obj[key];
    });
    return sorted;
}

/**
 * Generate a nonce (number used once) for API requests
 * @returns MD5 hash of current time + random number
 */
export function getNonce(): string {
    return md5((getClientTime() + 1000 * Math.random()).toString());
}

/**
 * Get current client time in seconds
 * @returns Current timestamp in seconds
 */
export function getClientTime(): number {
    return Math.round(Date.now() / 1000);
}

/**
 * Format version string to integer
 * @param version - Version string (e.g., "1.0")
 * @returns Version as integer (e.g., 10)
 */
export function versionFormat(version: string): number {
    return parseInt(version.replace(".", ""));
}

/**
 * Format date with custom pattern
 * @param date - Date object to format
 * @param pattern - Format pattern string
 * @returns Formatted date string
 */
export function formatDate(date: Date, pattern: string): string {
    const formatMap: { [key: string]: number | string } = {
        "M+": date.getMonth() + 1,
        "d+": date.getDate(),
        "h+": date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
        "H+": date.getHours(),
        "m+": date.getMinutes(),
        "s+": date.getSeconds(),
        "q+": Math.floor((date.getMonth() + 3) / 3),
        S: date.getMilliseconds()
    };

    // Handle year
    if (/(y+)/.test(pattern)) {
        pattern = pattern.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }

    // Handle day of week
    if (/(E+)/.test(pattern)) {
        const dayNames = "日一二三四五六";
        const prefix = RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? "星期" : "周") : "";
        pattern = pattern.replace(RegExp.$1, prefix + dayNames.charAt(date.getDay()));
    }

    // Handle other format tokens
    for (const key in formatMap) {
        if (new RegExp("(" + key + ")").test(pattern)) {
            const value = formatMap[key];
            const replacement = RegExp.$1.length === 1 ? 
                value.toString() : 
                ("00" + value).substr(value.toString().length);
            pattern = pattern.replace(RegExp.$1, replacement);
        }
    }

    return pattern;
}

/**
 * Create standardized API response object
 * @param code - Response code (default: 0)
 * @param msg - Response message (default: "")
 * @param data - Response data (default: {})
 * @returns Formatted response object
 */
export function response<T = any>(code: number = 0, msg: string = "", data: T = {} as T): ApiResponse<T> {
    return {
        code,
        msg,
        data
    };
}

/**
 * Generate signed request parameters for API calls
 * @param params - Request parameters object
 * @returns Parameters with timestamp, nonce, and signature added
 */
export function getSign<T extends Record<string, any>>(params: T): T & {
    timestamp: number;
    nonce: string;
    sign: string;
} {
    // Add timestamp and nonce
    const signedParams = {
        ...params,
        timestamp: getClientTime(),
        nonce: getNonce()
    };

    // Sort parameters
    const sortedParams = arrSort(signedParams);

    // Create signature string
    const paramStrings: string[] = [];
    for (const key in sortedParams) {
        paramStrings.push(key + "=" + sortedParams[key]);
    }

    const signatureString = paramStrings.join("&") + API_SECRET;
    const signature = md5(signatureString);

    return {
        ...sortedParams,
        sign: signature
    };
}

/**
 * Get server time (async operation)
 * @param callback - Callback function to handle server time response
 */
export function getServerTime(callback: (time: number) => void): void {
    Api.serverTime(callback);
}

/**
 * Utility functions for common operations
 */
export const FunctionUtils = {
    arrSort,
    getNonce,
    getClientTime,
    versionFormat,
    formatDate,
    response,
    getSign,
    getServerTime,

    /**
     * Deep clone an object
     */
    deepClone<T>(obj: T): T {
        if (obj === null || typeof obj !== "object") {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime()) as unknown as T;
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item)) as unknown as T;
        }
        
        if (typeof obj === "object") {
            const cloned = {} as T;
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
        
        return obj;
    },

    /**
     * Debounce function execution
     */
    debounce<T extends (...args: any[]) => any>(
        func: T, 
        wait: number
    ): (...args: Parameters<T>) => void {
        let timeout: NodeJS.Timeout | null = null;
        
        return (...args: Parameters<T>) => {
            if (timeout) {
                clearTimeout(timeout);
            }
            
            timeout = setTimeout(() => {
                func.apply(null, args);
            }, wait);
        };
    },

    /**
     * Throttle function execution
     */
    throttle<T extends (...args: any[]) => any>(
        func: T, 
        limit: number
    ): (...args: Parameters<T>) => void {
        let inThrottle = false;
        
        return (...args: Parameters<T>) => {
            if (!inThrottle) {
                func.apply(null, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * Check if value is empty (null, undefined, empty string, empty array, empty object)
     */
    isEmpty(value: any): boolean {
        if (value == null) return true;
        if (typeof value === "string") return value.length === 0;
        if (Array.isArray(value)) return value.length === 0;
        if (typeof value === "object") return Object.keys(value).length === 0;
        return false;
    },

    /**
     * Generate UUID v4
     */
    generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
};
