const fs = require('fs');
const path = require('path');

/**
 * JavaScript文件分析器
 * 用于分析Cocos Creator编译后的JavaScript文件中的定义类型
 */
class JSFileAnalyzer {
    constructor() {
        this.analysisResult = {
            filePath: '',
            fileContent: '',
            definitions: [],
            imports: [],
            exports: [],
            globalFunctions: [],
            enums: [],
            hasModuleExports: false,
            analysisErrors: []
        };
    }

    /**
     * 分析JavaScript文件
     * @param {string} filePath 文件路径
     * @returns {Object} 分析结果
     */
    analyzeFile(filePath) {
        try {
            this.analysisResult.filePath = filePath;
            this.analysisResult.fileContent = fs.readFileSync(filePath, 'utf8');
            
            console.log(`\n=== 分析文件: ${filePath} ===`);
            
            this._analyzeImports();
            this._analyzeExports();
            this._analyzeDefinitions();
            this._analyzeEnums();
            this._analyzeGlobalFunctions();
            
            this._printAnalysisResult();
            return this.analysisResult;
            
        } catch (error) {
            this.analysisResult.analysisErrors.push(`文件读取错误: ${error.message}`);
            console.error(`分析文件失败: ${filePath}`, error);
            return this.analysisResult;
        }
    }

    /**
     * 分析导入语句
     */
    _analyzeImports() {
        const content = this.analysisResult.fileContent;
        
        // 匹配 require 语句
        const requirePattern = /var\s+(\$\d*\$?\w+)\s*=\s*require\("([^"]+)"\)/g;
        let match;
        
        while ((match = requirePattern.exec(content)) !== null) {
            this.analysisResult.imports.push({
                variableName: match[1],
                modulePath: match[2],
                type: 'require'
            });
        }
    }

    /**
     * 分析导出语句
     */
    _analyzeExports() {
        const content = this.analysisResult.fileContent;
        
        // 检查是否有 module.exports 模式
        if (content.includes('Object.defineProperty(exports, "__esModule"')) {
            this.analysisResult.hasModuleExports = true;
        }
        
        // 匹配 exports.xxx = 语句
        const exportsPattern = /exports\.(\w+)\s*=\s*([^;]+);/g;
        let match;
        
        while ((match = exportsPattern.exec(content)) !== null) {
            this.analysisResult.exports.push({
                name: match[1],
                value: match[2].trim(),
                type: 'export'
            });
        }
    }

    /**
     * 分析类定义
     */
    _analyzeDefinitions() {
        const content = this.analysisResult.fileContent;
        
        // 分析 ccclass 装饰器模式的类
        this._analyzeCCClasses(content);
        
        // 分析普通的类继承模式
        this._analyzeRegularClasses(content);
        
        // 分析静态类/对象模式
        this._analyzeStaticClasses(content);
    }

    /**
     * 分析 ccclass 装饰器的类
     */
    _analyzeCCClasses(content) {
        // 匹配 cc__decorate([ccp_ccclass("ClassName")], _ctor) 模式
        const ccclassPattern = /cc__decorate\(\[ccp_ccclass\("([^"]+)"\)\],\s*(\w+)\)/g;
        let match;
        
        while ((match = ccclassPattern.exec(content)) !== null) {
            const className = match[1];
            const constructorName = match[2];
            
            // 查找对应的构造函数定义
            const classDefPattern = new RegExp(`var\\s+exp_${className}\\s*=\\s*function\\s*\\([^)]*\\)\\s*{[^}]*}\\([^)]+\\)`);
            const classDefMatch = content.match(classDefPattern);
            
            this.analysisResult.definitions.push({
                name: className,
                type: 'ccclass',
                constructorName: constructorName,
                hasDecorator: true,
                definition: classDefMatch ? classDefMatch[0] : null
            });
        }
    }

    /**
     * 分析普通类继承
     */
    _analyzeRegularClasses(content) {
        // 匹配 var exp_ClassName = function (e) { ... }(BaseClass) 模式 - 继承类
        const inheritancePattern = /var\s+exp_(\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?}\s*\(\$\d*\$?\w+\.[\w.]+\)/g;
        let match;

        while ((match = inheritancePattern.exec(content)) !== null) {
            const className = match[1];

            // 检查是否已经被识别为 ccclass
            const alreadyIdentified = this.analysisResult.definitions.some(def => def.name === className);
            if (!alreadyIdentified) {
                this.analysisResult.definitions.push({
                    name: className,
                    type: 'class',
                    hasDecorator: false,
                    definition: match[0]
                });
            }
        }

        // 匹配 var exp_ClassName = function () { ... }() 模式 - IIFE静态类
        const staticClassPattern = /var\s+exp_(\w+)\s*=\s*function\s*\(\)\s*{[\s\S]*?}\s*\(\s*\)/g;

        while ((match = staticClassPattern.exec(content)) !== null) {
            const className = match[1];

            // 检查是否已经被识别
            const alreadyIdentified = this.analysisResult.definitions.some(def => def.name === className);
            if (!alreadyIdentified) {
                // 检查是否包含静态方法定义
                const classContent = match[0];
                const staticMethodPattern = /_ctor\.\w+\s*=\s*function/g;
                const hasStaticMethods = staticMethodPattern.test(classContent);

                if (hasStaticMethods) {
                    this.analysisResult.definitions.push({
                        name: className,
                        type: 'static_class',
                        hasDecorator: false,
                        definition: match[0]
                    });
                }
            }
        }
    }

    /**
     * 分析静态类/对象
     */
    _analyzeStaticClasses(content) {
        // 匹配直接的对象定义模式
        const staticPattern = /var\s+(\w+)\s*=\s*{[\s\S]*?};/g;
        let match;
        
        while ((match = staticPattern.exec(content)) !== null) {
            const objectName = match[1];
            
            // 排除一些常见的非类对象
            if (!['cc__decorator', '_', 'i'].includes(objectName)) {
                this.analysisResult.definitions.push({
                    name: objectName,
                    type: 'static_object',
                    definition: match[0]
                });
            }
        }
    }

    /**
     * 分析枚举定义
     */
    _analyzeEnums() {
        const content = this.analysisResult.fileContent;

        // 匹配枚举模式 (function (e) { ... })(exports.EnumName || (exports.EnumName = {}))
        const enumPattern = /\(function\s*\(e\)\s*{([\s\S]*?)}\)\([^)]*exports\.(\w+)[^)]*\)/g;
        let match;

        while ((match = enumPattern.exec(content)) !== null) {
            const enumContent = match[1];
            const enumName = match[2];
            const enumValues = [];

            // 匹配数字枚举模式: e[e.VALUE = number] = "VALUE"
            const numericEnumPattern = /e\[e\.(\w+)\s*=\s*(\d+)\]\s*=\s*"(\w+)"/g;
            let valueMatch;

            while ((valueMatch = numericEnumPattern.exec(enumContent)) !== null) {
                enumValues.push({
                    name: valueMatch[1],
                    value: parseInt(valueMatch[2]),
                    stringValue: valueMatch[3],
                    type: 'numeric'
                });
            }

            // 匹配字符串枚举模式: e.VALUE = "string"
            const stringEnumPattern = /e\.(\w+)\s*=\s*"([^"]+)"/g;

            while ((valueMatch = stringEnumPattern.exec(enumContent)) !== null) {
                // 避免重复添加已经被数字枚举识别的值
                const alreadyExists = enumValues.some(v => v.name === valueMatch[1]);
                if (!alreadyExists) {
                    enumValues.push({
                        name: valueMatch[1],
                        value: valueMatch[2],
                        stringValue: valueMatch[2],
                        type: 'string'
                    });
                }
            }

            if (enumValues.length > 0) {
                this.analysisResult.enums.push({
                    name: enumName,
                    type: 'enum',
                    values: enumValues,
                    definition: match[0]
                });
            }
        }
    }

    /**
     * 分析全局函数
     */
    _analyzeGlobalFunctions() {
        const content = this.analysisResult.fileContent;
        
        // 匹配 window.functionName = function 模式
        const globalFuncPattern = /window\.(\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?};/g;
        let match;
        
        while ((match = globalFuncPattern.exec(content)) !== null) {
            this.analysisResult.globalFunctions.push({
                name: match[1],
                type: 'global_function',
                definition: match[0]
            });
        }
    }

    /**
     * 打印分析结果
     */
    _printAnalysisResult() {
        const result = this.analysisResult;
        
        console.log(`\n📁 文件: ${path.basename(result.filePath)}`);
        console.log(`📏 文件大小: ${result.fileContent.length} 字符`);
        
        if (result.imports.length > 0) {
            console.log(`\n📥 导入 (${result.imports.length}个):`);
            result.imports.forEach(imp => {
                console.log(`  - ${imp.variableName} <- "${imp.modulePath}"`);
            });
        }
        
        if (result.exports.length > 0) {
            console.log(`\n📤 导出 (${result.exports.length}个):`);
            result.exports.forEach(exp => {
                console.log(`  - ${exp.name} = ${exp.value.substring(0, 50)}${exp.value.length > 50 ? '...' : ''}`);
            });
        }
        
        if (result.definitions.length > 0) {
            console.log(`\n🏗️ 类定义 (${result.definitions.length}个):`);
            result.definitions.forEach(def => {
                const decorator = def.hasDecorator ? ' [装饰器]' : '';
                console.log(`  - ${def.name} (${def.type})${decorator}`);
            });
        }
        
        if (result.enums.length > 0) {
            console.log(`\n🔢 枚举 (${result.enums.length}个):`);
            result.enums.forEach((enumDef, index) => {
                console.log(`  - 枚举${index + 1}: ${enumDef.values.map(v => v.name).join(', ')}`);
            });
        }
        
        if (result.globalFunctions.length > 0) {
            console.log(`\n🌐 全局函数 (${result.globalFunctions.length}个):`);
            result.globalFunctions.forEach(func => {
                console.log(`  - window.${func.name}`);
            });
        }
        
        if (result.analysisErrors.length > 0) {
            console.log(`\n❌ 分析错误:`);
            result.analysisErrors.forEach(error => {
                console.log(`  - ${error}`);
            });
        }
        
        console.log(`\n📊 总结:`);
        console.log(`  - 类定义: ${result.definitions.length}个`);
        console.log(`  - 枚举: ${result.enums.length}个`);
        console.log(`  - 全局函数: ${result.globalFunctions.length}个`);
        console.log(`  - 导入: ${result.imports.length}个`);
        console.log(`  - 导出: ${result.exports.length}个`);
    }
}

/**
 * 批量分析目录中的所有JavaScript文件
 * @param {string} dirPath 目录路径
 * @returns {Array} 所有文件的分析结果
 */
function batchAnalyzeDirectory(dirPath) {
    const analyzer = new JSFileAnalyzer();
    const results = [];

    console.log(`\n🔍 开始批量分析目录: ${dirPath}`);

    try {
        const files = fs.readdirSync(dirPath);
        const jsFiles = files.filter(file => file.endsWith('.js'));

        console.log(`📁 找到 ${jsFiles.length} 个JavaScript文件`);

        jsFiles.forEach((file, index) => {
            const filePath = path.join(dirPath, file);
            console.log(`\n[${index + 1}/${jsFiles.length}] 分析: ${file}`);

            const result = analyzer.analyzeFile(filePath);
            results.push(result);

            // 重置分析器状态
            analyzer.analysisResult = {
                filePath: '',
                fileContent: '',
                definitions: [],
                imports: [],
                exports: [],
                globalFunctions: [],
                enums: [],
                hasModuleExports: false,
                analysisErrors: []
            };
        });

        // 打印总体统计
        printBatchSummary(results);

        // 生成详细报告
        generateDetailedReport(results);

    } catch (error) {
        console.error(`批量分析失败: ${error.message}`);
    }

    return results;
}

/**
 * 打印批量分析的总体统计
 */
function printBatchSummary(results) {
    console.log(`\n\n📊 ===== 批量分析总结 =====`);

    const stats = {
        totalFiles: results.length,
        filesWithClasses: 0,
        filesWithEnums: 0,
        filesWithGlobalFunctions: 0,
        filesWithExportsOnly: 0,
        totalClasses: 0,
        totalEnums: 0,
        totalGlobalFunctions: 0,
        ccclassCount: 0,
        regularClassCount: 0,
        staticObjectCount: 0
    };

    const fileTypes = {
        'single_class': [],
        'multi_class': [],
        'enum_only': [],
        'config_only': [],
        'global_functions': [],
        'mixed': []
    };

    results.forEach(result => {
        const fileName = path.basename(result.filePath);
        const classCount = result.definitions.length;
        const enumCount = result.enums.length;
        const globalFuncCount = result.globalFunctions.length;
        const exportCount = result.exports.length;

        // 统计数据
        if (classCount > 0) stats.filesWithClasses++;
        if (enumCount > 0) stats.filesWithEnums++;
        if (globalFuncCount > 0) stats.filesWithGlobalFunctions++;
        if (exportCount > 0 && classCount === 0 && enumCount === 0 && globalFuncCount === 0) {
            stats.filesWithExportsOnly++;
        }

        stats.totalClasses += classCount;
        stats.totalEnums += enumCount;
        stats.totalGlobalFunctions += globalFuncCount;

        // 统计类类型
        result.definitions.forEach(def => {
            if (def.type === 'ccclass') stats.ccclassCount++;
            else if (def.type === 'class') stats.regularClassCount++;
            else if (def.type === 'static_class') stats.staticObjectCount++;
            else if (def.type === 'static_object') stats.staticObjectCount++;
        });

        // 分类文件类型
        if (classCount === 1 && enumCount === 0 && globalFuncCount === 0) {
            fileTypes.single_class.push(fileName);
        } else if (classCount > 1 && enumCount === 0 && globalFuncCount === 0) {
            fileTypes.multi_class.push(fileName);
        } else if (classCount === 0 && enumCount > 0 && globalFuncCount === 0) {
            fileTypes.enum_only.push(fileName);
        } else if (classCount === 0 && enumCount === 0 && globalFuncCount === 0 && exportCount > 0) {
            fileTypes.config_only.push(fileName);
        } else if (classCount === 0 && enumCount === 0 && globalFuncCount > 0) {
            fileTypes.global_functions.push(fileName);
        } else if (classCount > 0 || enumCount > 0 || globalFuncCount > 0) {
            fileTypes.mixed.push(fileName);
        }
    });

    console.log(`📁 总文件数: ${stats.totalFiles}`);
    console.log(`🏗️ 包含类的文件: ${stats.filesWithClasses} (${(stats.filesWithClasses/stats.totalFiles*100).toFixed(1)}%)`);
    console.log(`🔢 包含枚举的文件: ${stats.filesWithEnums} (${(stats.filesWithEnums/stats.totalFiles*100).toFixed(1)}%)`);
    console.log(`🌐 包含全局函数的文件: ${stats.filesWithGlobalFunctions} (${(stats.filesWithGlobalFunctions/stats.totalFiles*100).toFixed(1)}%)`);
    console.log(`⚙️ 仅配置文件: ${stats.filesWithExportsOnly} (${(stats.filesWithExportsOnly/stats.totalFiles*100).toFixed(1)}%)`);

    console.log(`\n📈 定义统计:`);
    console.log(`  - 总类数: ${stats.totalClasses}`);
    console.log(`    - CCClass: ${stats.ccclassCount}`);
    console.log(`    - 普通类: ${stats.regularClassCount}`);
    console.log(`    - 静态类/对象: ${stats.staticObjectCount}`);
    console.log(`  - 总枚举数: ${stats.totalEnums}`);
    console.log(`  - 总全局函数数: ${stats.totalGlobalFunctions}`);

    console.log(`\n📋 文件类型分布:`);
    console.log(`  - 单类文件 (${fileTypes.single_class.length}个): ${fileTypes.single_class.slice(0, 5).join(', ')}${fileTypes.single_class.length > 5 ? '...' : ''}`);
    console.log(`  - 多类文件 (${fileTypes.multi_class.length}个): ${fileTypes.multi_class.slice(0, 5).join(', ')}${fileTypes.multi_class.length > 5 ? '...' : ''}`);
    console.log(`  - 枚举文件 (${fileTypes.enum_only.length}个): ${fileTypes.enum_only.slice(0, 5).join(', ')}${fileTypes.enum_only.length > 5 ? '...' : ''}`);
    console.log(`  - 配置文件 (${fileTypes.config_only.length}个): ${fileTypes.config_only.slice(0, 5).join(', ')}${fileTypes.config_only.length > 5 ? '...' : ''}`);
    console.log(`  - 全局函数文件 (${fileTypes.global_functions.length}个): ${fileTypes.global_functions.slice(0, 5).join(', ')}${fileTypes.global_functions.length > 5 ? '...' : ''}`);
    console.log(`  - 混合类型文件 (${fileTypes.mixed.length}个): ${fileTypes.mixed.slice(0, 5).join(', ')}${fileTypes.mixed.length > 5 ? '...' : ''}`);
}

/**
 * 生成详细的文件分析报告
 * @param {Array} results 分析结果数组
 * @param {string} outputPath 输出文件路径
 */
function generateDetailedReport(results, outputPath = 'analysis-report.md') {
    const reportLines = [];

    // 报告头部
    reportLines.push('# JavaScript文件分析详细报告');
    reportLines.push('');
    reportLines.push(`生成时间: ${new Date().toLocaleString()}`);
    reportLines.push(`分析文件总数: ${results.length}`);
    reportLines.push('');

    // 按文件类型分组
    const fileGroups = {
        'single_class': { title: '单类文件', files: [] },
        'multi_class': { title: '多类文件', files: [] },
        'enum_only': { title: '枚举文件', files: [] },
        'config_only': { title: '配置文件', files: [] },
        'global_functions': { title: '全局函数文件', files: [] },
        'mixed': { title: '混合类型文件', files: [] },
        'empty': { title: '空文件', files: [] }
    };

    // 分类文件
    results.forEach(result => {
        const fileName = path.basename(result.filePath);
        const classCount = result.definitions.length;
        const enumCount = result.enums.length;
        const globalFuncCount = result.globalFunctions.length;
        const exportCount = result.exports.length;
        const importCount = result.imports.length;

        const fileInfo = {
            fileName,
            filePath: result.filePath,
            classCount,
            enumCount,
            globalFuncCount,
            exportCount,
            importCount,
            definitions: result.definitions,
            enums: result.enums,
            globalFunctions: result.globalFunctions,
            imports: result.imports,
            exports: result.exports,
            fileSize: result.fileContent.length
        };

        // 分类逻辑
        if (classCount === 0 && enumCount === 0 && globalFuncCount === 0 && exportCount === 0) {
            fileGroups.empty.files.push(fileInfo);
        } else if (classCount === 1 && enumCount === 0 && globalFuncCount === 0) {
            fileGroups.single_class.files.push(fileInfo);
        } else if (classCount > 1 && enumCount === 0 && globalFuncCount === 0) {
            fileGroups.multi_class.files.push(fileInfo);
        } else if (classCount === 0 && enumCount > 0 && globalFuncCount === 0) {
            fileGroups.enum_only.files.push(fileInfo);
        } else if (classCount === 0 && enumCount === 0 && globalFuncCount === 0 && exportCount > 0) {
            fileGroups.config_only.files.push(fileInfo);
        } else if (classCount === 0 && enumCount === 0 && globalFuncCount > 0) {
            fileGroups.global_functions.files.push(fileInfo);
        } else {
            fileGroups.mixed.files.push(fileInfo);
        }
    });

    // 生成总体统计
    reportLines.push('## 📊 总体统计');
    reportLines.push('');
    Object.entries(fileGroups).forEach(([key, group]) => {
        if (group.files.length > 0) {
            reportLines.push(`- **${group.title}**: ${group.files.length}个文件`);
        }
    });
    reportLines.push('');

    // 为每个分组生成详细信息
    Object.entries(fileGroups).forEach(([key, group]) => {
        if (group.files.length === 0) return;

        reportLines.push(`## 📁 ${group.title} (${group.files.length}个)`);
        reportLines.push('');

        // 按文件名排序
        group.files.sort((a, b) => a.fileName.localeCompare(b.fileName));

        group.files.forEach(file => {
            reportLines.push(`### ${file.fileName}`);
            reportLines.push('');
            reportLines.push(`- **文件路径**: \`${file.filePath}\``);
            reportLines.push(`- **文件大小**: ${file.fileSize} 字符`);
            reportLines.push(`- **导入数量**: ${file.importCount}`);
            reportLines.push(`- **导出数量**: ${file.exportCount}`);

            if (file.classCount > 0) {
                reportLines.push(`- **类定义**: ${file.classCount}个`);
                file.definitions.forEach(def => {
                    const decorator = def.hasDecorator ? ' [装饰器]' : '';
                    reportLines.push(`  - ${def.name} (${def.type})${decorator}`);
                });
            }

            if (file.enumCount > 0) {
                reportLines.push(`- **枚举定义**: ${file.enumCount}个`);
                file.enums.forEach((enumDef, index) => {
                    const values = enumDef.values.map(v => v.name).join(', ');
                    reportLines.push(`  - 枚举${index + 1}: ${values.substring(0, 100)}${values.length > 100 ? '...' : ''}`);
                });
            }

            if (file.globalFuncCount > 0) {
                reportLines.push(`- **全局函数**: ${file.globalFuncCount}个`);
                file.globalFunctions.forEach(func => {
                    reportLines.push(`  - window.${func.name}`);
                });
            }

            if (file.importCount > 0) {
                reportLines.push(`- **主要导入**:`);
                file.imports.slice(0, 5).forEach(imp => {
                    reportLines.push(`  - ${imp.variableName} <- "${imp.modulePath}"`);
                });
                if (file.imports.length > 5) {
                    reportLines.push(`  - ... 还有${file.imports.length - 5}个导入`);
                }
            }

            reportLines.push('');
        });
    });

    // 写入文件
    const reportContent = reportLines.join('\n');
    fs.writeFileSync(outputPath, reportContent, 'utf8');
    console.log(`\n📄 详细报告已生成: ${outputPath}`);

    return reportContent;
}

// 如果直接运行此脚本
if (require.main === module) {
    const targetPath = process.argv[2];

    if (!targetPath) {
        console.log('使用方法:');
        console.log('  分析单个文件: node js-analyzer.js <文件路径>');
        console.log('  批量分析目录: node js-analyzer.js <目录路径>');
        console.log('示例:');
        console.log('  node js-analyzer.js scripts/activityCfg.js');
        console.log('  node js-analyzer.js scripts');
        process.exit(1);
    }

    const stats = fs.statSync(targetPath);

    if (stats.isDirectory()) {
        // 批量分析目录
        batchAnalyzeDirectory(targetPath);
    } else if (stats.isFile() && targetPath.endsWith('.js')) {
        // 分析单个文件
        const analyzer = new JSFileAnalyzer();
        analyzer.analyzeFile(targetPath);
    } else {
        console.error('错误: 请提供有效的JavaScript文件或目录路径');
        process.exit(1);
    }
}

module.exports = { JSFileAnalyzer, batchAnalyzeDirectory };
