import { TestController } from "./TestController";

/**
 * Mode unlock configuration interface
 */
export interface ModeUnlockConfig {
    mid: number;
    type: number;
    num: number;
}

/**
 * Redeem code configuration interface
 */
export interface RedeemCodeConfig {
    code: string;
    reward: number[][];
    rewardCount: number[][];
    type: number;
    startTime: number;
    endTime: number;
}

/**
 * Game configuration switches and parameters
 * Contains all game balance settings, feature toggles, and configuration arrays
 */
export class SwitchVo {
    // Core game settings
    public GM: number = 1;
    public isNotAd: boolean = false;
    public isLog: boolean = false;
    public isTaLog: number = 1;
    public shardAdapter: number = 1;

    // Skill and combat settings
    public superSkillCount: number[] = [1, 1];
    public weather: number[] = [1, 10, 5];
    public weatherWegiht: number[] = [1, 10, 2, 10, 3, 10, 4, 10, 5, 10, 6, 10, 7, 10];
    public fightStamina: number = 2;

    // Game modes and features
    public minigame: number[] = [0];
    public feedback: number[] = [0];
    public dmm: number[] = [1, 30];
    public tryWeaponList: string[] = ["10-4001-3-15", "18-4010-3-20", "25-4020-3-30"];
    public modeUnlock: ModeUnlockConfig[] = [{
        mid: 1,
        type: 5,
        num: 2
    }];

    // Gameplay balance
    public onGameHighWpRate: number = 3;
    public buildModeAirDropReward: number[] = [200, 300, 300, 100, 400, 30, 1000, 2];
    public buildModeAirDrop: number[] = [1, 1, 5, 3];
    public refreshSetting: number[] = [3600, 20];

    // Advertisement and monetization
    public adBuyStamina: number[] = [1, 10, 5];
    public diamondBuyStamina: number[] = [150, 15, 3];
    public lvSweep: number[] = [3, 2, 3];
    public adGetSilverCoin: number = 30;
    public adReviveSliverCoin: number = 100;

    // Level and difficulty settings
    public gridWeight: number[] = [1, 15];
    public lvDiff: number[][] = [[1, 0, 5, 0.4, 0.02], [1, 5, 8, 0.5, 0.03]];
    public lvDiffAd: number = 6;
    public lvDiffEasy: number[][] = [[1, 0, 5, 0.4, 0.02], [1, 5, 8, 0.5, 0.03]];

    // Equipment and item settings
    public lv2Equip: number[][] = [[1, 6, 15], [2, 8, 10], [3, 8, 5], [4, 5, 2], [9999, 5, 15]];
    public Lv1EquipAppear: number[][] = [[1, 0, 3, 0.3, 0.2, 0.2]];
    public grid12Appear: number[][] = [
        [1, 0, 3, 45, 20, 0], [1, 3, 6, 35, 10, 10], [1, 6, 10, 10, 5, 10],
        [2, 0, 3, 45, 20, 0], [2, 3, 6, 35, 10, 10], [2, 6, 10, 10, 5, 10],
        [3, 0, 3, 45, 20, 0], [3, 3, 6, 35, 10, 10], [3, 6, 10, 10, 5, 10], [3, 10, 15, 5, 5, 12], [3, 15, 20, 5, 5, 10],
        [4, 0, 3, 45, 20, 0], [4, 3, 6, 35, 10, 10], [4, 6, 10, 10, 5, 10], [4, 10, 15, 5, 5, 12], [4, 15, 20, 5, 5, 10],
        [5, 0, 3, 45, 20, 0], [5, 3, 6, 35, 10, 10], [5, 6, 10, 10, 5, 10], [5, 10, 15, 5, 5, 12], [5, 15, 20, 5, 5, 10],
        [6, 0, 3, 45, 20, 0], [6, 3, 6, 35, 10, 10], [6, 6, 10, 10, 5, 10], [6, 10, 15, 5, 5, 12], [6, 15, 20, 5, 5, 10],
        [7, 0, 3, 45, 20, 0], [7, 3, 6, 35, 10, 10], [7, 6, 10, 10, 5, 10], [7, 10, 15, 5, 5, 12], [7, 15, 20, 5, 5, 10],
        [8, 0, 3, 45, 20, 0], [8, 3, 6, 35, 10, 10], [8, 6, 10, 10, 5, 10], [8, 10, 15, 5, 5, 12], [8, 15, 20, 5, 5, 10],
        [9, 0, 3, 45, 20, 0], [9, 3, 6, 35, 10, 10], [9, 6, 10, 10, 5, 10], [9, 10, 15, 5, 5, 12], [9, 15, 20, 5, 5, 10],
        [10, 0, 3, 45, 20, 0], [10, 3, 6, 35, 10, 10], [10, 6, 10, 10, 5, 10], [10, 10, 15, 5, 5, 12], [10, 15, 20, 5, 5, 10],
        [11, 0, 3, 45, 20, 0], [11, 3, 6, 35, 10, 10], [11, 6, 10, 10, 5, 10], [11, 10, 15, 5, 5, 12], [11, 15, 20, 5, 5, 10],
        [12, 0, 3, 45, 20, 0], [12, 3, 6, 35, 10, 10], [12, 6, 10, 10, 5, 10], [12, 10, 15, 5, 5, 12], [12, 15, 20, 5, 5, 10],
        [13, 0, 3, 45, 20, 0], [13, 3, 6, 35, 10, 10], [13, 6, 10, 10, 5, 10], [13, 10, 15, 5, 5, 12], [13, 15, 20, 5, 5, 10],
        [14, 0, 3, 45, 20, 0], [14, 3, 6, 35, 10, 10], [14, 6, 10, 10, 5, 10], [14, 10, 15, 5, 5, 12], [14, 15, 20, 5, 5, 10],
        [15, 0, 3, 45, 20, 0], [15, 3, 6, 35, 10, 10], [15, 6, 10, 10, 5, 10], [15, 10, 15, 5, 5, 12], [15, 15, 20, 5, 5, 10],
        [16, 0, 3, 45, 20, 0], [16, 3, 6, 35, 10, 10], [16, 6, 10, 10, 5, 10], [16, 10, 15, 5, 5, 12], [16, 15, 20, 5, 5, 10],
        [9999, 0, 3, 45, 20, 0], [9999, 3, 6, 35, 10, 10], [9999, 6, 10, 10, 5, 10]
    ];

    // Shop and economy
    public shopDiscount: number[][] = [[0.5, 0.7, 0.9, 1], [20, 40, 40, 20]];
    public m20guideToggle: number[] = [1, 1];
    public adGetHeroFragmentCount: number = 2;
    public heroBfragmentWeight: number[][] = [
        [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
        [80, 30, 10, 9, 9, 7, 5, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1]
    ];
    public heroAfragmentWeight: number[][] = [
        [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40],
        [200, 80, 8, 6, 5, 4, 3, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1]
    ];
    public shopAdGift: number[][] = [[2, 3, 4], [1000, 5, 5]];

    // Game mechanics
    public adRevive: number = 1;
    public isEnterBackpack: number = 1;
    public limit2Grid: number = 0.7;
    public fit3GridEquip: number[] = [0.5, 0.2];
    public Lv3EquipAppear: number[] = [1, 3, 5, 0.25, 0.85];
    public speedUnlock: number = 1;
    public gameSpeed: number = 1;
    public equipbuff: number[][] = [[1, 0.7, 0.8], [2, 0.7, 0.8], [3, 0, 0]];
    public adGrid: number[] = [3, 0.6];

    // Gem system
    public gemAppearBegin: number[][] = [[1, 4, 99, 99, 0.4], [2, 0, 99, 99, 0.4]];
    public gemAppearWeight: number[][] = [
        [1, 0, 3, 5, 5], [1, 3, 6, 10, 15], [1, 6, 10, 15, 20],
        [2, 0, 3, 5, 5], [2, 3, 6, 10, 15], [2, 6, 10, 15, 20],
        [3, 0, 3, 5, 5], [3, 3, 6, 10, 15], [3, 6, 10, 15, 20], [3, 10, 15, 20, 25], [3, 15, 20, 15, 20],
        [9999, 0, 3, 5, 5], [9999, 3, 6, 10, 15], [9999, 6, 10, 15, 20]
    ];
    public gemAppearSwitch: number = 1;
    public gemSuccessWeight: number[][] = [[1, 1, 0.8, 0.6], [2, 1, 0.8, 0.6]];

    // Redeem codes and rewards
    public redeemCode: RedeemCodeConfig[] = [{
        code: "avge",
        reward: [[1, 2, 70], [1, 2, 20], [1, 2, 10]],
        rewardCount: [[1, 10], [2, 10]],
        type: 1,
        startTime: 1723450965,
        endTime: 1723450965
    }];

    // Daily and task systems
    public dailysign: number = 1;
    public task: number = 0;
    public greedGem: number[][] = [[1, 85, 2, 15], [1, 10, 2, 90]];
    public shopConfig: number[][] = [[11, 10], [2, 2], [9, 3], [25, 3]];
    public adRefreshEquip: number[] = [99, 1, 0.9, 0.1];
    public deadAnim: number = 0;
    public equip3selectAd: number = 1;
    public equip3SelectShow: number[] = [1, 1, 4, 6, 6];
    public eggGemAgain: number = 1;
    public eggAppearWeight: number[] = [0.1, 0.3, 0.5];
    public actCoinSweep: number = 1;
    public eliteLv: number = 1;

    // Mini games
    public GameKnife: number = 1;
    public GameZombieDef: number = 1;
    public dragonCrzay: number[][] = [[0, 20, 0.2, 1], [20, 40, 0.3, 1], [40, 70, 0.5, 1]];
    public dragonDiff: number[][] = [
        [60000, 20, 30, 0.3], [60000, 30, 70, 0.5], [60001, 30, 70, 0.5], [60001, 30, 70, 0.5]
    ];
    public miniGameLv: number[] = [3005, 3006];
    public miniGameAdUnlock: number = 1;
    public dragonRefreshBuff: number[] = [5, 5];
    public dragonBossDiff: number[][] = [
        [6024, 1, 0.3, 1, 1], [6024, 1, 0, 0.3, 2], [6024, 2, 0, 1, 5, 2],
        [6024, 2, 0.1, 0.3, 2], [6024, 2, 0, 0.1, 3]
    ];
    public diffSelectCfg: number[][] = [[1, 1], [1, 1]];
    public diffSelect: number = 1;
    public dragonBuffFree: number = 3;
    public dragonRevive: number = 3;
    public dragonBoxFree: number[][] = [[1, 0, 10], [2, 0, 3]];

    // Other switches
    public OVSwitch: number = 1;

    /**
     * Update switch configuration from server data
     * @param data - Configuration data from server
     */
    updateSwitchVo(data: { [key: string]: any }): void {
        Object.getOwnPropertyNames(this).forEach((propertyName: string) => {
            let configKey = propertyName;
            const underscoreIndex = propertyName.indexOf("_");
            
            // Handle properties starting with underscore
            if (underscoreIndex === 0) {
                configKey = propertyName.substring(underscoreIndex + 1);
            }
            
            // Update property if it exists in the data
            if (data.hasOwnProperty(configKey)) {
                (this as any)[propertyName] = data[configKey];
            }
        });

        // Initialize test controller if GM mode is enabled
        if (this.GM) {
            new TestController();
        }

        // Disable console.log if logging is disabled
        if (!this.isLog) {
            console.log = function() {};
        }

        // Set shard adapter based on environment
        this.shardAdapter = (window as any).wonderSdk?.isWebDev ? 0.1 : 1;
    }

    /**
     * Get configuration value by key
     * @param key - Configuration key
     * @returns Configuration value
     */
    getConfig<T = any>(key: string): T | undefined {
        return (this as any)[key];
    }

    /**
     * Set configuration value by key
     * @param key - Configuration key
     * @param value - Configuration value
     */
    setConfig(key: string, value: any): void {
        if (this.hasOwnProperty(key)) {
            (this as any)[key] = value;
        }
    }

    /**
     * Check if a feature is enabled
     * @param key - Feature key
     * @returns True if enabled
     */
    isFeatureEnabled(key: string): boolean {
        const value = this.getConfig(key);
        return Boolean(value);
    }
}
