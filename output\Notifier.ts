import { NotifyListener } from "./NotifyListener";
import { NotifyCaller } from "./NotifyCaller";

/**
 * Priority levels for event listeners
 */
export const PriorLowest = -200;
export const PriorLow = -100;
export const PriorMiddle = 0;
export const PriorHigh = 100;
export const PriorHighest = 200;

/**
 * Event listener function type
 */
export type EventListener = (...args: any[]) => void;

/**
 * Event caller function type
 */
export type EventCaller = (...args: any[]) => any;

/**
 * Notifier class for event-driven communication
 * Provides both listener (notification) and caller (request-response) patterns
 */
export class NotifierClass {
    private static _listener: NotifyListener = new NotifyListener();
    private static _caller: NotifyCaller = new NotifyCaller();

    /**
     * Get the listener instance
     */
    get listener(): NotifyListener {
        return NotifierClass._listener;
    }

    /**
     * Add an event listener
     * @param eventId - Event identifier
     * @param callback - Callback function
     * @param target - Target object for callback context
     * @param priority - Listener priority (default: PriorMiddle)
     */
    static addListener(
        eventId: number, 
        callback: EventListener, 
        target?: any, 
        priority: number = PriorMiddle
    ): void {
        this._listener.Register(eventId, callback, target, priority);
    }

    /**
     * Remove an event listener
     * @param eventId - Event identifier
     * @param callback - Callback function to remove
     * @param target - Target object
     */
    static removeListener(eventId: number, callback: EventListener, target?: any): void {
        this._listener.Unregister(eventId, callback, target);
    }

    /**
     * Conditionally add or remove an event listener
     * @param condition - Whether to add (true) or remove (false) the listener
     * @param eventId - Event identifier
     * @param callback - Callback function
     * @param target - Target object
     * @param priority - Listener priority (default: PriorMiddle)
     */
    static changeListener(
        condition: boolean,
        eventId: number, 
        callback: EventListener, 
        target?: any, 
        priority: number = PriorMiddle
    ): void {
        if (condition) {
            this.addListener(eventId, callback, target, priority);
        } else {
            this.removeListener(eventId, callback, target);
        }
    }

    /**
     * Send an event notification to all listeners
     * @param eventId - Event identifier
     * @param args - Arguments to pass to listeners
     */
    static send(eventId: number, ...args: any[]): void {
        this._listener.Send(eventId, ...args);
    }

    /**
     * Check if any listeners exist for an event
     * @param eventId - Event identifier
     * @returns True if listeners exist
     */
    static isExist(eventId: number): boolean {
        return this._listener.IsExist(eventId);
    }

    /**
     * Add a callable event handler
     * @param callId - Call identifier
     * @param callback - Callback function
     * @param target - Target object
     * @returns Success status
     */
    static addCall(callId: number, callback: EventCaller, target?: any): boolean {
        return this._caller.Register(callId, callback, target);
    }

    /**
     * Remove a callable event handler
     * @param callId - Call identifier
     * @param callback - Callback function to remove
     * @param target - Target object
     * @returns Success status
     */
    static removeCall(callId: number, callback: EventCaller, target?: any): boolean {
        return this._caller.Unregister(callId, callback, target);
    }

    /**
     * Conditionally add or remove a callable event handler
     * @param condition - Whether to add (true) or remove (false) the handler
     * @param callId - Call identifier
     * @param callback - Callback function
     * @param target - Target object
     */
    static changeCall(condition: boolean, callId: number, callback: EventCaller, target?: any): void {
        if (condition) {
            this.addCall(callId, callback, target);
        } else {
            this.removeCall(callId, callback, target);
        }
    }

    /**
     * Call an event handler and return its result
     * @param callId - Call identifier
     * @param args - Arguments to pass to the handler
     * @returns Result from the handler
     */
    static call(callId: number, ...args: any[]): any {
        return this._caller.Call(callId, ...args);
    }

    /**
     * Clear all listeners and callers
     */
    static clear(): void {
        this._listener.Clear();
        this._caller.Clear();
    }

    /**
     * Get all registered event IDs for listeners
     * @returns Array of event IDs
     */
    static getListenerEventIds(): number[] {
        return this._listener.GetEventIds();
    }

    /**
     * Get all registered call IDs
     * @returns Array of call IDs
     */
    static getCallIds(): number[] {
        return this._caller.GetCallIds();
    }

    /**
     * Get listener count for a specific event
     * @param eventId - Event identifier
     * @returns Number of listeners
     */
    static getListenerCount(eventId: number): number {
        return this._listener.GetListenerCount(eventId);
    }

    /**
     * Remove all listeners for a specific target
     * @param target - Target object
     */
    static removeAllListenersForTarget(target: any): void {
        this._listener.RemoveAllForTarget(target);
    }

    /**
     * Remove all callers for a specific target
     * @param target - Target object
     */
    static removeAllCallersForTarget(target: any): void {
        this._caller.RemoveAllForTarget(target);
    }

    /**
     * Get debug information about the notifier state
     * @returns Debug information object
     */
    static getDebugInfo(): {
        listenerCount: number;
        callerCount: number;
        events: number[];
        calls: number[];
    } {
        return {
            listenerCount: this.getListenerEventIds().length,
            callerCount: this.getCallIds().length,
            events: this.getListenerEventIds(),
            calls: this.getCallIds()
        };
    }
}

// Export singleton instance
export const Notifier = NotifierClass;
