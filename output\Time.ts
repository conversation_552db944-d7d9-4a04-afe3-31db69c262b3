import { Notifier } from "./Notifier";
import { NotifyID } from "./NotifyID";
import { Watcher } from "./Watcher";
import { ObjectPool } from "./ObjectPool";
import { MinSortList } from "./MinSortList";

export class TimeDelay {
    private _time: number = 0;
    private _index: number = 1;
    private _pool: ObjectPool<Watcher>;
    private _watchers: MinSortList<Watcher>;

    constructor() {
        this._pool = new ObjectPool<Watcher>(() => new Watcher());
        this._watchers = new MinSortList<Watcher>(this.compareTime);
    }

    get time(): number {
        return this._time;
    }

    get watchers(): MinSortList<Watcher> {
        return this._watchers;
    }

    /**
     * Delay execution of a callback
     * @param delayTime - Time to delay in seconds
     * @param callback - Function to call
     * @param target - Target object for callback
     * @param tag - Optional tag for identification
     * @param repeatCount - Number of times to repeat (1 = once)
     * @returns Watcher object for cancellation
     */
    delay(
        delayTime: number, 
        callback: Function, 
        target?: any, 
        tag?: any, 
        repeatCount: number = 1
    ): Watcher | null {
        if (callback == null) return null;
        if (delayTime == null || delayTime < 0) return null;

        const watcher = this._pool.pop();
        const id = ++this._index;
        watcher._setId(id);
        watcher.initWithCallback(this._time + delayTime, delayTime, callback, target, tag, repeatCount);
        this._watchers.add(watcher);
        return watcher;
    }

    /**
     * Cancel a delayed callback
     */
    doCancel(watcher: Watcher | null): void {
        if (watcher != null) {
            watcher.cancel();
        }
    }

    /**
     * Cancel by ID
     */
    cancelBy(id: number): void {
        if (id) {
            const watcher = this.watchers.element.find(w => w.id === id);
            if (watcher != null) {
                watcher.cancel();
            }
        }
    }

    /**
     * Cancel by tag
     */
    cancelByTag(tag: any): void {
        const watcher = this.watchers.element.find(w => w.tag === tag);
        if (watcher != null) {
            watcher.cancel();
        }
    }

    /**
     * Reset all delayed callbacks
     */
    reset(): void {
        this._index = 0;
        this._watchers.clear(this.doCancel, this);
    }

    /**
     * Destroy the delay system
     */
    destroy(): void {
        this.reset();
        this._pool.clear();
        this._pool = null!;
    }

    /**
     * Compare function for sorting watchers by time
     */
    private compareTime(a: Watcher, b: Watcher): number {
        if (a.nextTime < b.nextTime) {
            return -1;
        } else if (a.nextTime > b.nextTime) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * Update the delay system
     */
    onUpdate(deltaTime: number): void {
        this._time += deltaTime;
        let count = 0;

        while (true) {
            const watcher = this._watchers.peek();
            if (watcher == null || watcher.nextTime > this._time) {
                break;
            }

            const currentWatcher = this._watchers.pop();
            currentWatcher._callBack();

            if (currentWatcher.enable) {
                this._watchers.add(currentWatcher);
            } else {
                this._pool.push(currentWatcher);
            }

            if (++count > 2000) {
                break;
            }
        }
    }
}

class TimeManager {
    private _time: number = 0;
    private _deltaTime: number = 0;
    private _frameCount: number = 0;
    private _clientTimeMs: number = 0;
    public timeDelay: TimeDelay = new TimeDelay();
    private _gameTime: number = 0;
    private _serverTimeMs: number = 0;
    private _serverInitMs: number = 0;
    private _serverUpdateMs: number = 0;
    private _clientDate: Date = new Date();
    private _scaling: boolean = false;
    private _scale: number = 1;
    private _scaleDura: number = 0;
    private _scaleTimeout: number = 0;
    private _scaleSmooth: boolean = true;
    public midnight: number = 0;

    get time(): number {
        return this._time;
    }

    get deltaTime(): number {
        return this._deltaTime;
    }

    get frameCount(): number {
        return this._frameCount;
    }

    get clientTimeMs(): number {
        return Date.now();
    }

    get gameTime(): number {
        return this._gameTime;
    }

    get serverTimeMs(): number {
        this._serverTimeMs = this._serverInitMs + Date.now() - this._serverUpdateMs;
        return this._serverTimeMs;
    }

    get serverTime(): number {
        return Math.floor(this.serverTimeMs / 1000);
    }

    get clientDate(): Date {
        return this._clientDate;
    }

    get scale(): number {
        return cc.director.getScheduler().getTimeScale();
    }

    get isScaling(): boolean {
        return this._scaling;
    }

    /**
     * Set server time
     */
    setServerTime(serverTimeMs: number): void {
        this._serverTimeMs = serverTimeMs;
        this._serverInitMs = serverTimeMs;
        this._serverUpdateMs = Date.now();
        this.resetMidnight();
    }

    /**
     * Reset midnight calculation
     */
    resetMidnight(): void {
        const nextDay = new Date(this._serverTimeMs + 86400000);
        nextDay.setHours(0, 0, 0, 0);
        this.midnight = nextDay.getTime();
    }

    /**
     * Update time system
     */
    update(deltaTime: number): void {
        this._frameCount += 1;
        this._deltaTime = deltaTime;
        
        if (this._scaling) {
            deltaTime *= this.scale;
        }
        
        this._time += deltaTime;
        this._gameTime += deltaTime;
        this.updateScale();

        if (Notifier.isExist(NotifyID.Game_Update)) {
            Notifier.send(NotifyID.Game_Update, deltaTime);
        }

        if (this.serverTimeMs > this.midnight) {
            Notifier.send(NotifyID.Time_NewDay);
            this.resetMidnight();
        }

        this.timeDelay.onUpdate(deltaTime);
    }

    /**
     * Set time scale
     */
    setScale(scale: number, duration: number, smooth: boolean = true): void {
        this._scaling = true;
        this._scale = scale;
        this._scaleDura = duration;
        this._scaleTimeout = this._time + duration;
        this._scaleSmooth = smooth;
        cc.director.getScheduler().setTimeScale(scale);
        Notifier.send(NotifyID.Time_Scale, scale);
    }

    /**
     * Update scale smoothing
     */
    private updateScale(): void {
        if (this._scaling) {
            if (this._time > this._scaleTimeout) {
                this._scaling = false;
                cc.director.getScheduler().setTimeScale(1);
                Notifier.send(NotifyID.Time_Scale, 1);
                return;
            }

            if (this._scaleSmooth) {
                const currentScale = cc.misc.lerp(
                    this._scale, 
                    1, 
                    1 - (this._scaleTimeout - this._time) / this._scaleDura
                );
                cc.director.getScheduler().setTimeScale(currentScale);
                Notifier.send(NotifyID.Time_Scale, currentScale);
            }
        }
    }

    /**
     * Delay execution of a callback
     */
    delay(
        delayTime: number, 
        callback: Function, 
        target?: any, 
        tag?: any, 
        repeatCount: number = 1
    ): Watcher | null {
        return this.timeDelay.delay(delayTime, callback, target, tag, repeatCount);
    }

    /**
     * Cancel a delayed callback
     */
    doCancel(watcher: Watcher): void {
        this.timeDelay.doCancel(watcher);
    }

    /**
     * Reset time delay system
     */
    reset(): void {
        this.timeDelay.reset();
    }

    /**
     * Start game time logging
     */
    startGameTimeLog(): void {
        this._gameTime = 0;
    }
}

// Export singleton instance
export const Time = new TimeManager();
