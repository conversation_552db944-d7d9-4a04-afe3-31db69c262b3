import { CallID } from "./CallID";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { Time } from "./Time";
import { <PERSON>s<PERSON>eeper } from "./ResKeeper";
import { HttpClient } from "./HttpClient";
import { NetManager } from "./NetManager";
import { Notifier } from "./Notifier";

/**
 * Sort configuration interface
 */
export interface SortConfig {
    [key: string]: number;
}

/**
 * Asset handler function types
 */
export type LoadAssetHandler = (path: string) => Promise<any>;
export type UnloadAssetHandler = (asset: any) => void;

/**
 * View event handlers
 */
export type OnOpenEventHandler = (view: BaseView) => void;
export type OnCloseEventHandler = (view: BaseView) => void;

/**
 * MVC namespace containing all MVC-related classes
 */
export namespace MVC {
    /**
     * Base model class for data management
     */
    export class BaseModel {
        /**
         * Sort array by configuration
         * @param array - Array to sort
         * @param config - Sort configuration
         */
        sort<T>(array: T[], config: SortConfig): void {
            array.sort((a: any, b: any) => {
                for (const key in config) {
                    const multiplier = config[key];
                    const valueA = a[key];
                    const valueB = b[key];
                    
                    if (valueA != null && valueB != null) {
                        return multiplier * (valueA - valueB);
                    }
                }
                return 0;
            });
        }

        /**
         * Refresh data (override in subclasses)
         */
        refreshdata(): void {
            // Override in subclasses
        }
    }

    /**
     * Base controller class for business logic
     */
    export class BaseController {
        constructor() {
            ControllerContainer.add(this);
            this.registerAllProtocol();
        }

        /**
         * Handle data changes (override in subclasses)
         */
        changeListener(): void {
            // Override in subclasses
        }

        /**
         * Register network protocol handler
         * @param protocol - Protocol ID
         * @param handler - Response handler function
         * @param priority - Handler priority
         */
        registerProtocol(protocol: number, handler: Function, priority: number = 0): void {
            NetManager.instance.setResponseHandler(protocol, handler, this, priority);
        }

        /**
         * Make HTTP POST request
         * @param url - Request URL
         * @param data - Request data
         * @param responseType - Response type
         * @returns Promise with response
         */
        httpPost(url: string, data?: any, responseType: string = "json"): Promise<any> {
            return HttpClient.httpPost(url, data, responseType);
        }

        /**
         * Make HTTP GET request
         * @param url - Request URL
         * @param data - Request data
         * @param responseType - Response type
         * @returns Promise with response
         */
        httpGet(url: string, data?: any, responseType: string = "json"): Promise<any> {
            return HttpClient.httpGet(url, data, responseType);
        }

        /**
         * Register all protocols (override in subclasses)
         */
        registerAllProtocol(): void {
            // Override in subclasses
        }
    }

    /**
     * Model-specific controller with day tracking
     */
    export class MController extends BaseController {
        protected _model: BaseModel | null = null;

        /**
         * Setup controller with model
         * @param model - Model instance
         * @returns Setup success
         */
        setup(model: BaseModel): boolean {
            this._model = model;
            return true;
        }

        /**
         * Get associated model
         */
        get mode(): BaseModel | null {
            return this._model;
        }

        /**
         * Get class name for identification
         */
        get classname(): string {
            return "MController";
        }

        /**
         * Reset controller state (override in subclasses)
         */
        reset(): void {
            // Override in subclasses
        }

        /**
         * Check if it's a new day
         */
        get isNewDay(): boolean {
            const currentDay = new Date(Time.serverTimeMs).getDate();
            const storedDay = Manager.storage.getNumber(this.classname + "_Day", 0);
            return currentDay !== storedDay;
        }

        /**
         * Set current day in storage
         */
        setDay(): void {
            const currentDay = new Date(Time.serverTimeMs).getDate();
            Manager.storage.setNumber(this.classname + "_Day", currentDay);
        }
    }

    /**
     * Controller container for managing controller instances
     */
    export class ControllerContainer {
        private static _container: BaseController[] = [];

        /**
         * Add controller to container
         * @param controller - Controller instance
         */
        static add(controller: BaseController): void {
            const className = (controller as any).classname;
            if (this.getInstance(className) == null) {
                this._container.push(controller);
            } else {
                console.error("ControllerContainer.Add repeat:" + className);
            }
        }

        /**
         * Get controller instance by class name
         * @param className - Class name
         * @returns Controller instance or null
         */
        static getInstance<T extends BaseController>(className: string): T | null {
            for (const controller of this._container) {
                if ((controller as any).classname === className) {
                    return controller as T;
                }
            }
            return null;
        }

        /**
         * Reset all controllers
         */
        static reset(): void {
            for (const controller of this._container) {
                if ((controller as any).reset) {
                    (controller as any).reset();
                }
            }
        }

        /**
         * Get all controllers
         */
        static getAllControllers(): BaseController[] {
            return [...this._container];
        }

        /**
         * Clear all controllers
         */
        static clear(): void {
            this._container.length = 0;
        }
    }

    /**
     * Base view class for UI management
     */
    export class BaseView extends cc.Component {
        private static _loadAssetHandler: LoadAssetHandler | null = null;
        private static _unloadAssetHandler: UnloadAssetHandler | null = null;
        private static _onOpenEvent: OnOpenEventHandler | null = null;
        private static _onCloseEvent: OnCloseEventHandler | null = null;

        protected _isInit: boolean = false;
        protected _resKeeper: ResKeeper | null = null;

        /**
         * Get load asset handler
         */
        static get loadAssetHandler(): LoadAssetHandler | null {
            return this._loadAssetHandler;
        }

        /**
         * Get unload asset handler
         */
        static get unloadAssetHandler(): UnloadAssetHandler | null {
            return this._unloadAssetHandler;
        }

        /**
         * Initialize asset handlers
         * @param loadHandler - Load asset handler
         * @param unloadHandler - Unload asset handler
         */
        static initAssetHandler(loadHandler: LoadAssetHandler, unloadHandler: UnloadAssetHandler): void {
            this._loadAssetHandler = loadHandler;
            this._unloadAssetHandler = unloadHandler;
        }

        /**
         * Get open event handler
         */
        static get onOpenEvent(): OnOpenEventHandler | null {
            return this._onOpenEvent;
        }

        /**
         * Get close event handler
         */
        static get onCloseEvent(): OnCloseEventHandler | null {
            return this._onCloseEvent;
        }

        /**
         * Set view event handlers
         * @param onOpen - Open event handler
         * @param onClose - Close event handler
         */
        static setViewEvent(onOpen: OnOpenEventHandler, onClose: OnCloseEventHandler): void {
            this._onOpenEvent = onOpen;
            this._onCloseEvent = onClose;
        }

        /**
         * Initialize view (override in subclasses)
         */
        init(): void {
            if (!this._isInit) {
                this._isInit = true;
                this._resKeeper = new ResKeeper();
            }
        }

        /**
         * Open view
         */
        open(): void {
            this.init();
            this.node.active = true;
            if (BaseView._onOpenEvent) {
                BaseView._onOpenEvent(this);
            }
        }

        /**
         * Close view
         */
        close(): void {
            this.node.active = false;
            if (BaseView._onCloseEvent) {
                BaseView._onCloseEvent(this);
            }
        }

        /**
         * Destroy view
         */
        onDestroy(): void {
            if (this._resKeeper) {
                this._resKeeper.releaseAll();
                this._resKeeper = null;
            }
        }

        /**
         * Get resource keeper
         */
        get resKeeper(): ResKeeper | null {
            return this._resKeeper;
        }
    }
}
