import { <PERSON>, <PERSON><PERSON>anager, PoolSpawner, IPoolable } from "./Pool";
import { Manager } from "./Manager";
import { Time } from "./Time";

/**
 * Node loading states
 */
export enum NodeState {
    WaitLoad = 0,
    StartLoad = 1,
    Loaded = 2,
    TimeOut = 3,
    Die = 4
}

/**
 * Callback function type for node asset loading completion
 */
export type NodeAssetFinishCallback = (node: cc.Node | null) => void;

/**
 * Extended Node interface with pool item reference
 */
declare global {
    namespace cc {
        interface Node {
            nodeItem?: NodePoolItem;
        }
    }
}

/**
 * Node pool item that manages the lifecycle of a pooled node
 */
export class NodePoolItem implements IPoolable {
    private _state: NodeState = NodeState.WaitLoad;
    private _target: any = null;
    private _nodePath: string;
    private _node: cc.Node | null = null;
    private _onAssetFinishHandler: NodeAssetFinishCallback | null = null;
    
    public spawner?: PoolSpawner<NodePoolItem>;

    constructor(nodePath: string, spawner: PoolSpawner<NodePoolItem>) {
        this._nodePath = nodePath;
        this.spawner = spawner;
    }

    /**
     * Get the current node
     */
    get node(): cc.Node | null {
        return this._node;
    }

    /**
     * Set the node
     */
    set node(value: cc.Node | null) {
        this._node = value;
    }

    /**
     * Get current state
     */
    get state(): NodeState {
        return this._state;
    }

    /**
     * Get node path
     */
    get nodePath(): string {
        return this._nodePath;
    }

    /**
     * Called when item is returned to pool
     */
    unuse(): void {
        if (this._state === NodeState.Loaded) {
            if (this._node && this._node.parent) {
                this._node.removeFromParent(false);
            }
        } else if (this._state === NodeState.StartLoad) {
            this.setAssetState(NodeState.TimeOut);
        }

        this._target = null;
        this._onAssetFinishHandler = null;
    }

    /**
     * Called when item is retrieved from pool
     */
    reuse(): void {
        if (this._node && this._state === NodeState.Loaded) {
            // Delay callback to next frame
            Time.delay(0.01, () => {
                if (this._onAssetFinishHandler != null) {
                    this._onAssetFinishHandler.call(this._target, this._node);
                }
            });
        } else {
            this._loadAsset();
        }
    }

    /**
     * Destroy the pool item
     */
    destroy(): void {
        this._target = null;
        this._state = NodeState.Die;
        this._onAssetFinishHandler = null;

        if (this._node) {
            this._node.destroy();
            this._node = null;
        }
    }

    /**
     * Set asset loading state
     */
    setAssetState(state: NodeState): void {
        this._state = state;
    }

    /**
     * Set callback for when node asset loading finishes
     */
    setNodeAssetFinishCall(callback: NodeAssetFinishCallback, target?: any): void {
        this._onAssetFinishHandler = callback;
        this._target = target;
    }

    /**
     * Load the node asset
     */
    private _loadAsset(): void {
        this.setAssetState(NodeState.StartLoad);

        Manager.loader.loadPrefab(this._nodePath)
            .then((node: cc.Node) => {
                this._node = node;
                this._node.nodeItem = this;

                if (this._state === NodeState.StartLoad) {
                    this.setAssetState(NodeState.Loaded);
                    if (this._onAssetFinishHandler != null) {
                        this._onAssetFinishHandler.call(this._target, node);
                    }
                }
            })
            .catch((error: any) => {
                cc.error(error);
                this.setAssetState(NodeState.TimeOut);
                if (this._onAssetFinishHandler != null) {
                    this._onAssetFinishHandler.call(this._target, null);
                }
            });
    }
}

/**
 * Node pool manager for Cocos Creator nodes
 */
class NodePoolManager {
    private _pool: Pool<NodePoolItem> | null = null;
    private _poolType: string = "NodePool";
    public path: string = "";

    constructor() {
        this.initPool();
    }

    /**
     * Initialize the pool
     */
    private initPool(): void {
        if (!PoolManager.isExist(this._poolType)) {
            this._pool = PoolManager.create<NodePoolItem>(this._poolType, this);
        }
    }

    /**
     * Node creation delegate
     */
    nodeCreateDelegate(nodePath: string, spawner: PoolSpawner<NodePoolItem>): NodePoolItem {
        return new NodePoolItem(nodePath, spawner);
    }

    /**
     * Return a node to the pool
     */
    despawn(item: NodePoolItem, destroy: boolean = false): void {
        if (this._pool != null) {
            this._pool.despawn(item, destroy);
        } else if (item != null) {
            item.destroy();
        }
    }

    /**
     * Get a node from the pool
     */
    spawn(nodePath: string): NodePoolItem | null {
        if (this._pool == null) {
            return null;
        }

        if (!this._pool.isExitsSpawner(nodePath)) {
            this._pool.createSpawner(nodePath, this.nodeCreateDelegate.bind(this), null, 0, 1);
            this.path = nodePath;
        }

        return this._pool.spawn(nodePath);
    }

    /**
     * Spawn a node asynchronously
     * @param nodePath - Path to the node prefab
     * @returns Promise that resolves with the loaded node pool item
     */
    spawnAsync(nodePath: string): Promise<NodePoolItem> {
        return new Promise((resolve, reject) => {
            const item = this.spawn(nodePath);
            if (!item) {
                reject(new Error("Failed to spawn node pool item"));
                return;
            }

            item.setNodeAssetFinishCall((node: cc.Node | null) => {
                if (node) {
                    resolve(item);
                } else {
                    this.despawn(item);
                    reject(new Error("Failed to load node asset"));
                }
            });
        });
    }

    /**
     * Spawn a node and get the actual cc.Node
     * @param nodePath - Path to the node prefab
     * @returns Promise that resolves with the loaded cc.Node
     */
    spawnNode(nodePath: string): Promise<cc.Node> {
        return this.spawnAsync(nodePath).then(item => {
            if (item.node) {
                return item.node;
            } else {
                throw new Error("Node is null after loading");
            }
        });
    }

    /**
     * Clear all pooled nodes
     */
    clear(): void {
        if (this._pool != null) {
            this._pool._clear();
        }
    }

    /**
     * Destroy the node pool
     */
    destroy(): void {
        if (this._pool != null) {
            this._pool._destroy();
            this._pool = null;
        }
    }

    /**
     * Get pool statistics
     */
    getStats(): { spawnerCount: number; totalObjects: number } | null {
        return PoolManager.getPoolStats(this._poolType);
    }

    /**
     * Check if a spawner exists for the given path
     */
    hasSpawner(nodePath: string): boolean {
        return this._pool ? this._pool.isExitsSpawner(nodePath) : false;
    }

    /**
     * Preload nodes for a specific path
     */
    preload(nodePath: string, count: number): Promise<void> {
        const promises: Promise<NodePoolItem>[] = [];
        
        for (let i = 0; i < count; i++) {
            promises.push(this.spawnAsync(nodePath).then(item => {
                this.despawn(item);
                return item;
            }));
        }

        return Promise.all(promises).then(() => {});
    }
}

// Export singleton instance
export const NodePool = new NodePoolManager();
