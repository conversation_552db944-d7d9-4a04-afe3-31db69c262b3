import { Manager } from "./Manager";

// Log level constants
export const LOG_LEVEL = {
    VERBOSE: "verbose",
    INFO: "info", 
    WARNING: "warning",
    ERROR: "error",
    NONE: "none"
} as const;

export type LogLevel = typeof LOG_LEVEL[keyof typeof LOG_LEVEL];

/**
 * Check if a log level is valid
 */
export function isLegalLogLevel(level: string): level is LogLevel {
    return [
        LOG_LEVEL.VERBOSE, 
        LOG_LEVEL.INFO, 
        LOG_LEVEL.WARNING, 
        LOG_LEVEL.ERROR, 
        LOG_LEVEL.NONE
    ].includes(level as LogLevel);
}

/**
 * Format date string with pattern
 */
function formatDate(date: Date, pattern: string): string {
    const formatMap: { [key: string]: number | string } = {
        "M+": date.getMonth() + 1,
        "d+": date.getDate(),
        "h+": date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
        "H+": date.getHours(),
        "m+": date.getMinutes(),
        "s+": date.getSeconds(),
        "q+": Math.floor((date.getMonth() + 3) / 3),
        S: date.getMilliseconds()
    };

    // Handle year
    if (/(y+)/.test(pattern)) {
        pattern = pattern.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }

    // Handle day of week
    if (/(E+)/.test(pattern)) {
        const dayNames = "日一二三四五六";
        const prefix = RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? "星期" : "周") : "";
        pattern = pattern.replace(RegExp.$1, prefix + dayNames.charAt(date.getDay()));
    }

    // Handle other format tokens
    for (const key in formatMap) {
        if (new RegExp("(" + key + ")").test(pattern)) {
            const value = formatMap[key];
            const replacement = RegExp.$1.length === 1 ? 
                value.toString() : 
                ("00" + value).substr(value.toString().length);
            pattern = pattern.replace(RegExp.$1, replacement);
        }
    }

    return pattern;
}

/**
 * Logger class for structured logging
 */
export default class Logger {
    static id: number = 0;
    static LOG_LEVEL = LOG_LEVEL;

    private level: LogLevel;
    private prefix: string;
    private id: number;

    constructor(level: LogLevel = "none", prefix: string = "LOG") {
        this.level = level;
        this.prefix = prefix;
        this.id = Logger.id;
    }

    /**
     * Check if a log level is valid
     */
    static isLegalLogLevel(level: string): level is LogLevel {
        return isLegalLogLevel(level);
    }

    /**
     * Get formatted print prefix
     */
    private getPrintPrefix(logLevel: string): string {
        this.id++;
        const timestamp = formatDate(new Date(), "yyyy-MM-ddThh:mm:ss.SZ");
        return `[wonder-js-sdk] ${timestamp} [${logLevel.toLocaleUpperCase()}][${this.prefix}#${this.id}]:`;
    }

    /**
     * Set log level
     */
    setLogLevel(level: LogLevel): LogLevel {
        return this.level = level;
    }

    /**
     * Get current log level
     */
    getLogLevel(): LogLevel {
        return this.level;
    }

    /**
     * Log info message
     */
    info(...args: any[]): void {
        if (Manager.vo.switchVo.isTaLog) {
            const allowedLevels = ["info"];
            if (allowedLevels.includes(this.level)) {
                console.log(this.getPrintPrefix("info"), ...args);
            }
        }
    }

    /**
     * Log warning message
     */
    warn(...args: any[]): void {
        const allowedLevels = ["info", "warn"];
        if (allowedLevels.includes(this.level)) {
            console.warn(this.getPrintPrefix("warn"), ...args);
        }
    }

    /**
     * Log error message
     */
    error(...args: any[]): void {
        const allowedLevels = ["info", "warn", "error", "verbose"];
        if (allowedLevels.includes(this.level)) {
            console.error(this.getPrintPrefix("error"), ...args);
        }
    }

    /**
     * Log verbose message
     */
    verbose(...args: any[]): void {
        const allowedLevels = ["info", "warn", "error", "verbose"];
        if (allowedLevels.includes(this.level)) {
            console.error(this.getPrintPrefix("verbose"), ...args);
        }
    }

    /**
     * Log debug message (alias for verbose)
     */
    debug(...args: any[]): void {
        this.verbose(...args);
    }

    /**
     * Log with custom level
     */
    log(level: LogLevel, ...args: any[]): void {
        switch (level) {
            case LOG_LEVEL.INFO:
                this.info(...args);
                break;
            case LOG_LEVEL.WARNING:
                this.warn(...args);
                break;
            case LOG_LEVEL.ERROR:
                this.error(...args);
                break;
            case LOG_LEVEL.VERBOSE:
                this.verbose(...args);
                break;
            default:
                // NONE level - do nothing
                break;
        }
    }

    /**
     * Create a child logger with a new prefix
     */
    child(prefix: string): Logger {
        return new Logger(this.level, `${this.prefix}.${prefix}`);
    }

    /**
     * Check if a log level would be output
     */
    isLevelEnabled(level: LogLevel): boolean {
        const levelHierarchy = [
            LOG_LEVEL.NONE,
            LOG_LEVEL.ERROR,
            LOG_LEVEL.WARNING,
            LOG_LEVEL.INFO,
            LOG_LEVEL.VERBOSE
        ];

        const currentLevelIndex = levelHierarchy.indexOf(this.level);
        const checkLevelIndex = levelHierarchy.indexOf(level);

        return currentLevelIndex >= checkLevelIndex && this.level !== LOG_LEVEL.NONE;
    }
}
